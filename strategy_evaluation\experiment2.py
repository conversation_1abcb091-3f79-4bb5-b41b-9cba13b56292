"""
Experiment 2: Impact of Market Impact on Strategy Performance
"""
import matplotlib.pyplot as plt
import pandas as pd
import datetime as dt
import numpy as np

from StrategyLearner import StrategyLearner
from marketsimcode import compute_portvals
from util import get_data


def run_experiment2():
    # In-sample period
    start_date = dt.datetime(2008, 1, 1)
    end_date = dt.datetime(2009, 12, 31)
    starting_value = 100000
    comm = 9.95
    ticker = "JPM"

    # Test different impact values
    impact_values = [0.0, 0.005, 0.01, 0.02, 0.05]

    # Initialize data structures
    results = []

    for impact in impact_values:
        print(f"Testing with impact = {impact:.3f}")

        # Create and train Strategy Learner
        learner = StrategyLearner(impact=impact, commission=comm, verbose=False)
        learner.add_evidence(symbol=ticker, sd=start_date, ed=end_date, sv=starting_value)

        # Get trades from Strategy Learner
        trades = learner.testPolicy(symbol=ticker, sd=start_date, ed=end_date, sv=starting_value)

        # Calculate portfolio value
        port_value = compute_portvals(trades, start_val=starting_value, commission=comm, impact=impact)

        # Calculate metrics
        daily_returns = port_value.pct_change().dropna()
        cumulative_return = (port_value.iloc[-1] / port_value.iloc[0]) - 1
        mean_daily_return = daily_returns.mean()
        std_daily_return = daily_returns.std()
        sharpe_ratio = np.sqrt(252) * mean_daily_return / std_daily_return if std_daily_return != 0 else 0

        # Count number of trades
        num_trades = (trades != 0).sum().iloc[0]

        results.append({
            'impact': impact,
            'cumulative_return': cumulative_return,
            'mean_daily_return': mean_daily_return,
            'std_daily_return': std_daily_return,
            'sharpe_ratio': sharpe_ratio,
            'num_trades': num_trades
        })

        print(f"Cumulative return: {cumulative_return:.4f}")
        print(f"Number of trades: {num_trades}")
        print()

    # Convert results to DataFrame
    results_df = pd.DataFrame(results)
    results_df.set_index('impact', inplace=True)

    # Create charts
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

    # Chart 1: Cumulative Returns vs Impact
    ax1.plot(results_df.index, results_df['cumulative_return'], 'b-o')
    ax1.set_xlabel('Impact')
    ax1.set_ylabel('Cumulative Return')
    ax1.set_title('Cumulative Return vs Impact')
    ax1.grid(True)

    # Chart 2: Number of Trades vs Impact
    ax2.plot(results_df.index, results_df['num_trades'], 'r-o')
    ax2.set_xlabel('Impact')
    ax2.set_ylabel('Number of Trades')
    ax2.set_title('Number of Trades vs Impact')
    ax2.grid(True)

    # Chart 3: Sharpe Ratio vs Impact
    ax3.plot(results_df.index, results_df['sharpe_ratio'], 'g-o')
    ax3.set_xlabel('Impact')
    ax3.set_ylabel('Sharpe Ratio')
    ax3.set_title('Sharpe Ratio vs Impact')
    ax3.grid(True)

    # Chart 4: Mean Daily Return vs Impact
    ax4.plot(results_df.index, results_df['mean_daily_return'], 'm-o')
    ax4.set_xlabel('Impact')
    ax4.set_ylabel('Mean Daily Return')
    ax4.set_title('Mean Daily Return vs Impact')
    ax4.grid(True)

    plt.tight_layout()
    plt.savefig("experiment2_impact_analysis.png", dpi=300, bbox_inches='tight')
    plt.close()

    return results_df


def author():
    """
    :return: The GT username of the student
    :rtype: str
    """
    return "jjang333"


if __name__ == "__main__":
    run_experiment2()