"""
Experiment 2: Impact of Market Impact on Strategy Performance
"""
import pandas as pd
import numpy as np
import datetime as dt
import matplotlib.pyplot as plt
from StrategyLearner import StrategyLearner
import marketsimcode as ms
import util as ut

def run_experiment2():
    """
    Analyze how different market impact values affect the Strategy Learner's performance
    """
    symbol = "JPM"
    start_val = 100000
    commission = 9.95
    
    # Date ranges
    in_sample_sd = dt.datetime(2008, 1, 1)
    in_sample_ed = dt.datetime(2009, 12, 31)
    out_sample_sd = dt.datetime(2010, 1, 1)
    out_sample_ed = dt.datetime(2011, 12, 31)
    
    # Different impact values to test
    impact_values = [0.000, 0.005, 0.010, 0.020, 0.050]
    
    # Storage for results
    results = {
        'impact': [],
        'in_sample_return': [],
        'out_sample_return': [],
        'in_sample_sharpe': [],
        'out_sample_sharpe': [],
        'in_sample_trades': [],
        'out_sample_trades': []
    }
    
    print("EXPERIMENT 2: IMPACT OF MARKET IMPACT ON STRATEGY PERFORMANCE")
    print("="*70)
    
    for impact in impact_values:
        print(f"\nTesting with impact = {impact:.3f}")
        print("-" * 40)
        
        # Initialize and train strategy learner
        strategy_learner = StrategyLearner(verbose=False, impact=impact, commission=commission)
        strategy_learner.add_evidence(symbol=symbol, sd=in_sample_sd, ed=in_sample_ed, sv=start_val)
        
        # Get trades for both periods
        trades_in = strategy_learner.testPolicy(symbol=symbol, sd=in_sample_sd, ed=in_sample_ed, sv=start_val)
        trades_out = strategy_learner.testPolicy(symbol=symbol, sd=out_sample_sd, ed=out_sample_ed, sv=start_val)
        
        # Calculate portfolio values
        portvals_in = ms.compute_portvals(trades_in, start_val=start_val, commission=commission, impact=impact)
        portvals_out = ms.compute_portvals(trades_out, start_val=start_val, commission=commission, impact=impact)
        
        # Calculate performance metrics
        def get_performance_metrics(portvals):
            daily_returns = portvals.pct_change().dropna()
            cumulative_return = (portvals.iloc[-1] / portvals.iloc[0]) - 1
            sharpe_ratio = (daily_returns.mean() / daily_returns.std()) * np.sqrt(252)
            return cumulative_return, sharpe_ratio
        
        in_return, in_sharpe = get_performance_metrics(portvals_in)
        out_return, out_sharpe = get_performance_metrics(portvals_out)
        
        # Count trades
        in_trades = (trades_in != 0).sum().sum()
        out_trades = (trades_out != 0).sum().sum()
        
        # Store results
        results['impact'].append(impact)
        results['in_sample_return'].append(in_return)
        results['out_sample_return'].append(out_return)
        results['in_sample_sharpe'].append(in_sharpe)
        results['out_sample_sharpe'].append(out_sharpe)
        results['in_sample_trades'].append(in_trades)
        results['out_sample_trades'].append(out_trades)
        
        print(f"In-sample return: {in_return:.4f}")
        print(f"Out-sample return: {out_return:.4f}")
        print(f"In-sample Sharpe: {in_sharpe:.4f}")
        print(f"Out-sample Sharpe: {out_sharpe:.4f}")
        print(f"In-sample trades: {in_trades}")
        print(f"Out-sample trades: {out_trades}")

    # Create individual plots using util.plot_data
    print("\nGenerating individual analysis charts...")

    # Plot 1: Cumulative Returns vs Impact using plot_data
    returns_df = pd.DataFrame(index=results['impact'])
    returns_df['In-sample'] = results['in_sample_return']
    returns_df['Out-of-sample'] = results['out_sample_return']
    ut.plot_data(returns_df,
                title="Cumulative Return vs Market Impact",
                xlabel="Market Impact",
                ylabel="Cumulative Return")

    # Plot 2: Sharpe Ratio vs Impact using plot_data
    sharpe_df = pd.DataFrame(index=results['impact'])
    sharpe_df['In-sample'] = results['in_sample_sharpe']
    sharpe_df['Out-of-sample'] = results['out_sample_sharpe']
    ut.plot_data(sharpe_df,
                title="Sharpe Ratio vs Market Impact",
                xlabel="Market Impact",
                ylabel="Sharpe Ratio")

    # Plot 3: Number of Trades vs Impact using plot_data
    trades_df = pd.DataFrame(index=results['impact'])
    trades_df['In-sample'] = results['in_sample_trades']
    trades_df['Out-of-sample'] = results['out_sample_trades']
    ut.plot_data(trades_df,
                title="Trading Activity vs Market Impact",
                xlabel="Market Impact",
                ylabel="Number of Trades")

    # Create combined visualization for saving
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # Plot 1: Cumulative Returns vs Impact
    ax1.plot(results['impact'], results['in_sample_return'], 'b-o', label='In-sample', linewidth=2, markersize=8)
    ax1.plot(results['impact'], results['out_sample_return'], 'r-s', label='Out-of-sample', linewidth=2, markersize=8)
    ax1.set_xlabel('Market Impact')
    ax1.set_ylabel('Cumulative Return')
    ax1.set_title('Cumulative Return vs Market Impact')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Sharpe Ratio vs Impact
    ax2.plot(results['impact'], results['in_sample_sharpe'], 'b-o', label='In-sample', linewidth=2, markersize=8)
    ax2.plot(results['impact'], results['out_sample_sharpe'], 'r-s', label='Out-of-sample', linewidth=2, markersize=8)
    ax2.set_xlabel('Market Impact')
    ax2.set_ylabel('Sharpe Ratio')
    ax2.set_title('Sharpe Ratio vs Market Impact')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Number of Trades vs Impact
    ax3.plot(results['impact'], results['in_sample_trades'], 'b-o', label='In-sample', linewidth=2, markersize=8)
    ax3.plot(results['impact'], results['out_sample_trades'], 'r-s', label='Out-of-sample', linewidth=2, markersize=8)
    ax3.set_xlabel('Market Impact')
    ax3.set_ylabel('Number of Trades')
    ax3.set_title('Trading Activity vs Market Impact')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Plot 4: Return per Trade vs Impact
    in_return_per_trade = [ret/trades if trades > 0 else 0 for ret, trades in zip(results['in_sample_return'], results['in_sample_trades'])]
    out_return_per_trade = [ret/trades if trades > 0 else 0 for ret, trades in zip(results['out_sample_return'], results['out_sample_trades'])]
    
    ax4.plot(results['impact'], in_return_per_trade, 'b-o', label='In-sample', linewidth=2, markersize=8)
    ax4.plot(results['impact'], out_return_per_trade, 'r-s', label='Out-of-sample', linewidth=2, markersize=8)
    ax4.set_xlabel('Market Impact')
    ax4.set_ylabel('Return per Trade')
    ax4.set_title('Return per Trade vs Market Impact')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('experiment2_impact_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()  # Close the figure to avoid showing it twice
    
    # Print detailed results table
    print("\n" + "="*90)
    print("DETAILED RESULTS TABLE")
    print("="*90)
    print(f"{'Impact':<8} {'In-Ret':<8} {'Out-Ret':<8} {'In-Sharpe':<10} {'Out-Sharpe':<10} {'In-Trades':<10} {'Out-Trades':<10}")
    print("-" * 90)
    
    for i in range(len(results['impact'])):
        print(f"{results['impact'][i]:<8.3f} {results['in_sample_return'][i]:<8.4f} {results['out_sample_return'][i]:<8.4f} "
              f"{results['in_sample_sharpe'][i]:<10.4f} {results['out_sample_sharpe'][i]:<10.4f} "
              f"{results['in_sample_trades'][i]:<10d} {results['out_sample_trades'][i]:<10d}")
    
    # Analysis and insights
    print("\n" + "="*90)
    print("KEY INSIGHTS FROM EXPERIMENT 2")
    print("="*90)
    
    # Find optimal impact values
    best_in_sample_idx = np.argmax(results['in_sample_return'])
    best_out_sample_idx = np.argmax(results['out_sample_return'])
    best_in_sharpe_idx = np.argmax(results['in_sample_sharpe'])
    best_out_sharpe_idx = np.argmax(results['out_sample_sharpe'])
    
    print(f"• Best in-sample return at impact = {results['impact'][best_in_sample_idx]:.3f} ({results['in_sample_return'][best_in_sample_idx]:.4f})")
    print(f"• Best out-of-sample return at impact = {results['impact'][best_out_sample_idx]:.3f} ({results['out_sample_return'][best_out_sample_idx]:.4f})")
    print(f"• Best in-sample Sharpe ratio at impact = {results['impact'][best_in_sharpe_idx]:.3f} ({results['in_sample_sharpe'][best_in_sharpe_idx]:.4f})")
    print(f"• Best out-of-sample Sharpe ratio at impact = {results['impact'][best_out_sharpe_idx]:.3f} ({results['out_sample_sharpe'][best_out_sharpe_idx]:.4f})")
    
    # Analyze trading activity correlation
    impact_trade_corr_in = np.corrcoef(results['impact'], results['in_sample_trades'])[0, 1]
    impact_trade_corr_out = np.corrcoef(results['impact'], results['out_sample_trades'])[0, 1]
    
    print(f"• Correlation between impact and trading activity (in-sample): {impact_trade_corr_in:.3f}")
    print(f"• Correlation between impact and trading activity (out-of-sample): {impact_trade_corr_out:.3f}")
    
    # Performance degradation analysis
    avg_in_return = np.mean(results['in_sample_return'])
    avg_out_return = np.mean(results['out_sample_return'])
    performance_degradation = (avg_in_return - avg_out_return) / avg_in_return * 100
    
    print(f"• Average performance degradation from in-sample to out-of-sample: {performance_degradation:.1f}%")
    
    # Impact sensitivity analysis
    return_sensitivity = (results['in_sample_return'][0] - results['in_sample_return'][-1]) / results['impact'][-1]
    print(f"• Return sensitivity to impact (in-sample): {return_sensitivity:.2f} return per unit impact")
    
    print("\nCONCLUSIONS:")
    print("-" * 50)
    if impact_trade_corr_in < -0.5:
        print("• Higher market impact significantly reduces trading activity")
    else:
        print("• Market impact has moderate effect on trading activity")
    
    if results['in_sample_return'][0] > results['in_sample_return'][-1]:
        print("• Strategy performance degrades with higher market impact")
    else:
        print("• Strategy shows resilience to market impact")
    
    if performance_degradation > 20:
        print("• Strategy shows significant overfitting (>20% performance degradation)")
    elif performance_degradation > 10:
        print("• Strategy shows moderate overfitting (10-20% performance degradation)")
    else:
        print("• Strategy shows good generalization (<10% performance degradation)")

def author():
    return 'jjang333'

if __name__ == "__main__":
    run_experiment2()