import numpy as np


class DTLearner(object):
    def __init__(self, leaf_size=1, verbose=False):
        """
        Constructor method
        """
        self.leaf_size = leaf_size
        self.verbose = verbose
        self.tree = None

    def add_evidence(self, data_x, data_y):
        """
        Add training data to learner

        :param data_x: A set of feature values used to train the learner
        :type data_x: numpy.ndarray
        :param data_y: The value we are attempting to predict given the X data
        :type data_y: numpy.ndarray
        """
        # Combine X and Y data for easier manipulation
        data = np.column_stack((data_x, data_y))
        
        # Build the tree
        self.tree = self._build_tree(data)

    def _build_tree(self, data):
        """
        Recursively build the decision tree
        
        :param data: Combined X and Y data
        :type data: numpy.ndarray
        :return: Tree as NDArray
        :rtype: numpy.ndarray
        """
        # Base cases
        if data.shape[0] <= self.leaf_size:
            # Create leaf node: [leaf_indicator, y_value, NA, NA]
            return np.array([[-1, np.mean(data[:, -1]), np.nan, np.nan]])
        
        if np.all(data[:, -1] == data[0, -1]):
            # All Y values are the same
            return np.array([[-1, data[0, -1], np.nan, np.nan]])
        
        # Find best feature to split on (highest absolute correlation with Y)
        best_feature = self._find_best_feature(data)
        
        # Calculate split value (median of the best feature)
        split_val = np.median(data[:, best_feature])
        
        # Handle case where all values are the same (can't split)
        if np.all(data[:, best_feature] == split_val):
            return np.array([[-1, np.mean(data[:, -1]), np.nan, np.nan]])
        
        # Split data
        left_data = data[data[:, best_feature] <= split_val]
        right_data = data[data[:, best_feature] > split_val]
        
        # Handle edge case where one side is empty
        if left_data.shape[0] == 0 or right_data.shape[0] == 0:
            return np.array([[-1, np.mean(data[:, -1]), np.nan, np.nan]])
        
        # Recursively build left and right subtrees
        left_tree = self._build_tree(left_data)
        right_tree = self._build_tree(right_data)
        
        # Create root node: [feature_index, split_value, left_start, right_start]
        root = np.array([[best_feature, split_val, 1, left_tree.shape[0] + 1]])
        
        # Combine root, left tree, and right tree
        return np.vstack((root, left_tree, right_tree))

    def _find_best_feature(self, data):
        """
        Find the feature with highest absolute correlation with Y
        
        :param data: Combined X and Y data
        :type data: numpy.ndarray
        :return: Index of best feature
        :rtype: int
        """
        y = data[:, -1]
        num_features = data.shape[1] - 1
        
        best_corr = -1
        best_feature = 0
        
        for i in range(num_features):
            x = data[:, i]
            # Calculate correlation, handle case where std is 0
            if np.std(x) == 0 or np.std(y) == 0:
                corr = 0
            else:
                corr = abs(np.corrcoef(x, y)[0, 1])
                if np.isnan(corr):
                    corr = 0
            
            if corr > best_corr:
                best_corr = corr
                best_feature = i
        
        return best_feature

    def query(self, points):
        """
        Estimate a set of test points given the model we built.

        :param points: A numpy array with each row corresponding to a specific query.
        :type points: numpy.ndarray
        :return: The predicted result of the input data according to the trained model
        :rtype: numpy.ndarray
        """
        if self.tree is None:
            raise ValueError("Model has not been trained yet. Call add_evidence first.")
        
        predictions = np.zeros(points.shape[0])
        
        for i, point in enumerate(points):
            predictions[i] = self._query_single(point, 0)
        
        return predictions

    def _query_single(self, point, node_index):
        """
        Query a single point through the tree
        
        :param point: Single data point to query
        :type point: numpy.ndarray
        :param node_index: Current node index in the tree
        :type node_index: int
        :return: Predicted value
        :rtype: float
        """
        node = self.tree[node_index]
        
        # Check if this is a leaf node
        if node[0] == -1:
            return node[1]
        
        # Internal node - make decision
        feature_index = int(node[0])
        split_val = node[1]
        
        if point[feature_index] <= split_val:
            # Go left
            left_start = int(node[2])
            return self._query_single(point, node_index + left_start)
        else:
            # Go right
            right_start = int(node[3])
            return self._query_single(point, node_index + right_start)
        
    def author(self):  
        return 'jjang333'


if __name__ == "__main__":
    print("the secret clue is 'zzyzx'")
