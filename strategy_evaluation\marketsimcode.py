"""
Market Simulator Code
This module provides functionality to compute portfolio values based on trades.
"""

import pandas as pd
import numpy as np
from util import get_data

def author():
    return 'jjang333'

def compute_portvals(orders_df, start_val=1000000, commission=9.95, impact=0.005):
    """
    Compute portfolio values based on trades DataFrame.

    Args:
        orders_df: DataFrame with trades (index=dates, columns=symbols, values=shares)
        start_val: Starting portfolio value
        commission: Commission per trade
        impact: Market impact (slippage) as a fraction

    Returns:
        Series with portfolio values indexed by date
    """
    # Get the date range and symbols
    start_date = orders_df.index.min()
    end_date = orders_df.index.max()
    symbols = orders_df.columns.tolist()

    # Get price data for all symbols
    dates = pd.date_range(start_date, end_date)
    prices_df = get_data(symbols, dates, addSPY=True, colname='Adj Close')
    prices_df = prices_df.drop(['SPY'], axis=1, errors='ignore')

    # Initialize portfolio tracking
    cash = start_val
    holdings = pd.Series(0.0, index=symbols)
    portvals = pd.Series(0.0, index=prices_df.index)

    # Process each day
    for date in prices_df.index:
        # Check if there are any trades on this date
        if date in orders_df.index:
            for symbol in symbols:
                shares = orders_df.loc[date, symbol]
                if shares != 0:  # There's a trade
                    price = prices_df.loc[date, symbol]

                    # Apply market impact
                    if shares > 0:  # Buying
                        effective_price = price * (1 + impact)
                    else:  # Selling
                        effective_price = price * (1 - impact)

                    # Calculate trade value and update cash
                    trade_value = shares * effective_price
                    cash -= trade_value
                    cash -= commission  # Apply commission

                    # Update holdings
                    holdings[symbol] += shares

        # Calculate portfolio value for this date
        holdings_value = 0.0
        for symbol in symbols:
            if holdings[symbol] != 0:
                holdings_value += holdings[symbol] * prices_df.loc[date, symbol]

        portvals[date] = cash + holdings_value

    return portvals