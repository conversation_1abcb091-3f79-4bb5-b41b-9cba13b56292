"""
Auto-Grader Test Script for Strategy Learner
Tests all requirements from the grading rubric
"""

import pandas as pd
import numpy as np
import datetime as dt
import time
from StrategyLearner import StrategyLearner

def test_symbol(symbol, name, min_in_sample_return=None, min_out_sample_return=None):
    """Test a single symbol against auto-grader requirements"""
    print(f"\n{'='*60}")
    print(f"TESTING {name} ({symbol})")
    print(f"{'='*60}")
    
    # Test parameters
    sd_train = dt.datetime(2008, 1, 1)
    ed_train = dt.datetime(2009, 12, 31)
    sd_test = dt.datetime(2010, 1, 1)
    ed_test = dt.datetime(2011, 12, 31)
    sv = 100000
    
    results = {
        'add_evidence_time': 0,
        'testPolicy_in_time': 0,
        'testPolicy_out_time': 0,
        'consistent_results': False,
        'in_sample_return': 0,
        'out_sample_return': 0,
        'benchmark_return': 0,
        'passes_requirements': True
    }
    
    try:
        # Test 1: add_evidence() completes within 25 seconds
        print(f"1. Testing add_evidence() for {symbol}...")
        learner = StrategyLearner(verbose=False, impact=0.0, commission=0.0)
        
        start_time = time.time()
        learner.add_evidence(symbol=symbol, sd=sd_train, ed=ed_train, sv=sv)
        add_evidence_time = time.time() - start_time
        results['add_evidence_time'] = add_evidence_time
        
        print(f"   ✓ add_evidence() completed in {add_evidence_time:.2f} seconds")
        if add_evidence_time > 25:
            print(f"   ❌ FAILED: Took {add_evidence_time:.2f}s > 25s limit")
            results['passes_requirements'] = False
        
        # Test 2: testPolicy() in-sample completes within 5 seconds
        print(f"2. Testing testPolicy() in-sample speed for {symbol}...")
        start_time = time.time()
        trades_in_1 = learner.testPolicy(symbol=symbol, sd=sd_train, ed=ed_train, sv=sv)
        testPolicy_in_time = time.time() - start_time
        results['testPolicy_in_time'] = testPolicy_in_time
        
        print(f"   ✓ testPolicy() in-sample completed in {testPolicy_in_time:.2f} seconds")
        if testPolicy_in_time > 5:
            print(f"   ❌ FAILED: Took {testPolicy_in_time:.2f}s > 5s limit")
            results['passes_requirements'] = False
        
        # Test 3: testPolicy() returns same result when called twice
        print(f"3. Testing testPolicy() consistency for {symbol}...")
        trades_in_2 = learner.testPolicy(symbol=symbol, sd=sd_train, ed=ed_train, sv=sv)
        
        # Compare trades DataFrames
        if trades_in_1.equals(trades_in_2):
            results['consistent_results'] = True
            print(f"   ✓ testPolicy() returns consistent results")
        else:
            print(f"   ❌ FAILED: testPolicy() returns different results on consecutive calls")
            results['passes_requirements'] = False
        
        # Test 4: testPolicy() out-of-sample completes within 5 seconds
        print(f"4. Testing testPolicy() out-of-sample speed for {symbol}...")
        start_time = time.time()
        trades_out = learner.testPolicy(symbol=symbol, sd=sd_test, ed=ed_test, sv=sv)
        testPolicy_out_time = time.time() - start_time
        results['testPolicy_out_time'] = testPolicy_out_time
        
        print(f"   ✓ testPolicy() out-of-sample completed in {testPolicy_out_time:.2f} seconds")
        if testPolicy_out_time > 5:
            print(f"   ❌ FAILED: Took {testPolicy_out_time:.2f}s > 5s limit")
            results['passes_requirements'] = False
        
        # Test 5: Calculate performance metrics
        print(f"5. Testing performance requirements for {symbol}...")
        
        # Calculate portfolio values
        import marketsimcode as ms
        
        # In-sample performance
        portvals_in = ms.compute_portvals(trades_in_1, start_val=sv, commission=0.0, impact=0.0)
        in_sample_return = (portvals_in.iloc[-1] / portvals_in.iloc[0]) - 1.0
        results['in_sample_return'] = in_sample_return
        
        # Out-of-sample performance
        portvals_out = ms.compute_portvals(trades_out, start_val=sv, commission=0.0, impact=0.0)
        out_sample_return = (portvals_out.iloc[-1] / portvals_out.iloc[0]) - 1.0
        results['out_sample_return'] = out_sample_return
        
        # Benchmark performance (buy and hold)
        benchmark_trades = trades_in_1.copy()
        benchmark_trades.values[:, :] = 0
        benchmark_trades.iloc[0, 0] = 1000  # Buy on first day
        benchmark_trades.iloc[-1, 0] = -1000  # Sell on last day
        
        benchmark_portvals = ms.compute_portvals(benchmark_trades, start_val=sv, commission=0.0, impact=0.0)
        benchmark_return = (benchmark_portvals.iloc[-1] / benchmark_portvals.iloc[0]) - 1.0
        results['benchmark_return'] = benchmark_return
        
        print(f"   In-sample return: {in_sample_return:.4f} ({in_sample_return*100:.2f}%)")
        print(f"   Out-of-sample return: {out_sample_return:.4f} ({out_sample_return*100:.2f}%)")
        print(f"   Benchmark return: {benchmark_return:.4f} ({benchmark_return*100:.2f}%)")
        
        # Check specific requirements
        if min_in_sample_return is not None:
            if in_sample_return >= min_in_sample_return:
                print(f"   ✓ In-sample return {in_sample_return*100:.2f}% >= {min_in_sample_return*100:.0f}%")
            else:
                print(f"   ❌ FAILED: In-sample return {in_sample_return*100:.2f}% < {min_in_sample_return*100:.0f}%")
                results['passes_requirements'] = False
        
        if min_out_sample_return is not None:
            if out_sample_return >= min_out_sample_return:
                print(f"   ✓ Out-of-sample return {out_sample_return*100:.2f}% >= {min_out_sample_return*100:.0f}%")
            else:
                print(f"   ❌ FAILED: Out-of-sample return {out_sample_return*100:.2f}% < {min_out_sample_return*100:.0f}%")
                results['passes_requirements'] = False
        
        # Check if beats benchmark (for AAPL and UNH)
        if symbol in ['AAPL', 'UNH']:
            if in_sample_return > benchmark_return:
                print(f"   ✓ In-sample return beats benchmark ({in_sample_return*100:.2f}% > {benchmark_return*100:.2f}%)")
            else:
                print(f"   ❌ FAILED: In-sample return doesn't beat benchmark ({in_sample_return*100:.2f}% <= {benchmark_return*100:.2f}%)")
                results['passes_requirements'] = False
        
    except Exception as e:
        print(f"   ❌ CRASHED: {str(e)}")
        results['passes_requirements'] = False
    
    return results

def test_impact_sensitivity():
    """Test that strategy accounts for different impact values"""
    print(f"\n{'='*60}")
    print(f"TESTING IMPACT SENSITIVITY")
    print(f"{'='*60}")
    
    symbol = "JPM"  # Use a known symbol
    sd = dt.datetime(2008, 1, 1)
    ed = dt.datetime(2009, 12, 31)
    sv = 100000
    
    try:
        # Test with low impact
        learner_low = StrategyLearner(verbose=False, impact=0.0, commission=0.0)
        learner_low.add_evidence(symbol=symbol, sd=sd, ed=ed, sv=sv)
        trades_low = learner_low.testPolicy(symbol=symbol, sd=sd, ed=ed, sv=sv)
        
        # Test with high impact
        learner_high = StrategyLearner(verbose=False, impact=0.05, commission=0.0)
        learner_high.add_evidence(symbol=symbol, sd=sd, ed=ed, sv=sv)
        trades_high = learner_high.testPolicy(symbol=symbol, sd=sd, ed=ed, sv=sv)
        
        # Check if trades are different
        if not trades_low.equals(trades_high):
            print("   ✓ Strategy produces different trades for different impact values")
            return True
        else:
            print("   ❌ FAILED: Strategy produces same trades regardless of impact")
            return False
            
    except Exception as e:
        print(f"   ❌ CRASHED: {str(e)}")
        return False

def main():
    """Run all auto-grader tests"""
    print("STRATEGY LEARNER AUTO-GRADER TEST")
    print("="*60)
    
    all_results = {}
    
    # Test each symbol with specific requirements
    symbols_to_test = [
        ("ML4T-220", "ML4T-220", 1.0, 1.0),  # >100% in-sample and out-of-sample
        ("AAPL", "AAPL", None, None),  # Beat benchmark in-sample
        ("SINE_FAST_NOISE", "SINE_FAST_NOISE", 2.0, None),  # >200% in-sample
        ("UNH", "UNH", None, None),  # Beat benchmark in-sample
    ]
    
    for symbol, name, min_in, min_out in symbols_to_test:
        try:
            results = test_symbol(symbol, name, min_in, min_out)
            all_results[symbol] = results
        except Exception as e:
            print(f"Failed to test {symbol}: {e}")
            all_results[symbol] = {'passes_requirements': False}
    
    # Test impact sensitivity
    impact_test_passed = test_impact_sensitivity()
    
    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY OF RESULTS")
    print(f"{'='*60}")
    
    total_points = 0
    max_points = 70
    
    for symbol, results in all_results.items():
        if results['passes_requirements']:
            print(f"✓ {symbol}: PASSED")
            if symbol == "ML4T-220":
                total_points += 15  # 1+2+2+5+5
            else:
                total_points += 15  # 1+2+2+5+5
        else:
            print(f"❌ {symbol}: FAILED")
    
    if impact_test_passed:
        print(f"✓ Impact Sensitivity: PASSED")
        total_points += 10  # 5+5 for both withheld test cases
    else:
        print(f"❌ Impact Sensitivity: FAILED")
    
    print(f"\nEstimated Score: {total_points}/{max_points} points")
    print(f"Percentage: {(total_points/max_points)*100:.1f}%")

if __name__ == "__main__":
    main()
