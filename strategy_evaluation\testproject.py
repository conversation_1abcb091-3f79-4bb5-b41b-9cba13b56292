"""
Test Project - Entry point for running all components of the Strategy Evaluation project.
This file runs Manual Strategy, Strategy Learner, and both experiments.
"""

import datetime as dt
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Import project modules
from ManualStrategy import ManualStrategy
from StrategyLearner import StrategyLearner
import experiment1
import experiment2

def main():
    """
    Main function to run all components of the project.
    """
    print("="*80)
    print("STRATEGY EVALUATION PROJECT - COMPLETE ANALYSIS")
    print("="*80)
    print("Author: jjang333")
    print("Symbol: JPM")
    print("In-sample period: 2008-01-01 to 2009-12-31")
    print("Out-of-sample period: 2010-01-01 to 2011-12-31")
    print("Starting value: $100,000")
    print("="*80)
    
    try:
        # 1. Manual Strategy Analysis
        print("\n" + "="*60)
        print("1. MANUAL STRATEGY ANALYSIS")
        print("="*60)
        
        manual_strategy = ManualStrategy(verbose=False, impact=0.005, commission=9.95)
        print("Running Manual Strategy analysis...")
        manual_strategy.generate_charts()
        print("Manual Strategy analysis completed.")
        
        # 2. Strategy Learner Training and Testing
        print("\n" + "="*60)
        print("2. STRATEGY LEARNER ANALYSIS")
        print("="*60)
        
        # Test basic functionality
        symbol = "JPM"
        start_val = 100000
        in_sample_sd = dt.datetime(2008, 1, 1)
        in_sample_ed = dt.datetime(2009, 12, 31)
        out_sample_sd = dt.datetime(2010, 1, 1)
        out_sample_ed = dt.datetime(2011, 12, 31)
        
        strategy_learner = StrategyLearner(verbose=False, impact=0.005, commission=9.95)
        print("Training Strategy Learner...")
        strategy_learner.add_evidence(symbol=symbol, sd=in_sample_sd, ed=in_sample_ed, sv=start_val)
        
        print("Testing Strategy Learner...")
        trades_in = strategy_learner.testPolicy(symbol=symbol, sd=in_sample_sd, ed=in_sample_ed, sv=start_val)
        trades_out = strategy_learner.testPolicy(symbol=symbol, sd=out_sample_sd, ed=out_sample_ed, sv=start_val)
        
        print(f"Strategy Learner generated {(trades_in != 0).sum().sum()} in-sample trades")
        print(f"Strategy Learner generated {(trades_out != 0).sum().sum()} out-of-sample trades")
        print("Strategy Learner analysis completed.")
        
        # 3. Experiment 1: Strategy Comparison
        print("\n" + "="*60)
        print("3. EXPERIMENT 1: STRATEGY COMPARISON")
        print("="*60)
        
        print("Running Experiment 1...")
        experiment1.run_experiment1()
        print("Experiment 1 completed.")
        
        # 4. Experiment 2: Impact Analysis
        print("\n" + "="*60)
        print("4. EXPERIMENT 2: IMPACT ANALYSIS")
        print("="*60)
        
        print("Running Experiment 2...")
        results_df = experiment2.run_experiment2()
        print("Experiment 2 completed.")
        
        # 5. Summary
        print("\n" + "="*60)
        print("5. PROJECT SUMMARY")
        print("="*60)
        
        print("All analyses completed successfully!")
        print("\nGenerated files:")
        print("- manual_strategy_comparison.png")
        print("- experiment1_comparison.png") 
        print("- experiment2_impact_analysis.png")
        
        print("\nKey findings:")
        print("1. Manual Strategy uses rule-based trading with technical indicators")
        print("2. Strategy Learner uses Q-Learning to optimize trading decisions")
        print("3. Both strategies compared against buy-and-hold benchmark")
        print("4. Market impact significantly affects trading performance and behavior")
        
        print("\nProject execution completed successfully!")
        
    except Exception as e:
        print(f"\nError during execution: {str(e)}")
        print("Please check that all required files are present:")
        print("- QLearner.py (or your chosen learner)")
        print("- indicators.py")
        print("- marketsimcode.py")
        print("- util.py")
        raise e

def test_individual_components():
    """
    Test individual components separately for debugging.
    """
    print("Testing individual components...")
    
    # Test Manual Strategy
    try:
        ms = ManualStrategy()
        trades = ms.testPolicy()
        print(f"✓ Manual Strategy: Generated {(trades != 0).sum().sum()} trades")
    except Exception as e:
        print(f"✗ Manual Strategy failed: {e}")
    
    # Test Strategy Learner
    try:
        sl = StrategyLearner()
        sl.add_evidence()
        trades = sl.testPolicy()
        print(f"✓ Strategy Learner: Generated {(trades != 0).sum().sum()} trades")
    except Exception as e:
        print(f"✗ Strategy Learner failed: {e}")
    
    # Test Experiments
    try:
        experiment1.run_experiment1()
        print("✓ Experiment 1: Completed")
    except Exception as e:
        print(f"✗ Experiment 1 failed: {e}")
    
    try:
        experiment2.run_experiment2()
        print("✓ Experiment 2: Completed")
    except Exception as e:
        print(f"✗ Experiment 2 failed: {e}")

def author():
    return 'jjang333'

if __name__ == "__main__":
    # Set random seed for reproducibility
    np.random.seed(903658570)
    
    # Run main analysis
    main()
    
    # Uncomment below for component testing
    # test_individual_components()
