""""""  		  	   		 	 	 			  		 			 	 	 		 		 	
"""  		  	   		 	 	 			  		 			 	 	 		 		 	
Test a learner.  (c) 2015 <PERSON>ch  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
Copyright 2018, Georgia Institute of Technology (Georgia Tech)  		  	   		 	 	 			  		 			 	 	 		 		 	
Atlanta, Georgia 30332  		  	   		 	 	 			  		 			 	 	 		 		 	
All Rights Reserved  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
Template code for CS 4646/7646  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
Georgia Tech asserts copyright ownership of this template and all derivative  		  	   		 	 	 			  		 			 	 	 		 		 	
works, including solutions to the projects assigned in this course. Students  		  	   		 	 	 			  		 			 	 	 		 		 	
and other users of this template code are advised not to share it with others  		  	   		 	 	 			  		 			 	 	 		 		 	
or to make it available on publicly viewable websites including repositories  		  	   		 	 	 			  		 			 	 	 		 		 	
such as github and gitlab.  This copyright statement should not be removed  		  	   		 	 	 			  		 			 	 	 		 		 	
or edited.  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
We do grant permission to share solutions privately with non-students such  		  	   		 	 	 			  		 			 	 	 		 		 	
as potential employers. However, sharing with other current or future  		  	   		 	 	 			  		 			 	 	 		 		 	
students of CS 7646 is prohibited and subject to being investigated as a  		  	   		 	 	 			  		 			 	 	 		 		 	
GT honor code violation.  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
-----do not edit anything above this line---  		  	   		 	 	 			  		 			 	 	 		 		 	
"""  		  	   		 	 	 			  		 			 	 	 		 		 			  		 			 	 	 		 		 	
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import math
import DTLearner as dt
import RTLearner as rt
import BagLearner as bl
import LinRegLearner as lrl
import InsaneLearner as it
import sys
from dataclasses import dataclass
from typing import Optional

@dataclass
class EvaluationResult:
    rmse_in: float
    rmse_out: float
    corr_in: float
    corr_out: float
    mae_in: Optional[float] = None
    mae_out: Optional[float] = None
    model_size: Optional[int] = None

def eval_learner(data, learner_class, **kwargs):
    trainX, trainY, testX, testY = data
    learner = learner_class(**kwargs)
    learner.add_evidence(trainX, trainY)

    # In-sample
    predY_in = learner.query(trainX)
    rmse_in = math.sqrt(((trainY - predY_in) ** 2).mean())
    corr_in = np.corrcoef(predY_in, trainY)[0, 1]
    mae_in = np.mean(np.abs(trainY - predY_in))

    # Out-of-sample
    predY_out = learner.query(testX)
    rmse_out = math.sqrt(((testY - predY_out) ** 2).mean())
    corr_out = np.corrcoef(predY_out, testY)[0, 1]
    mae_out = np.mean(np.abs(testY - predY_out))

    # Estimate model size (if learner has .tree)
    model_size = len(getattr(learner, "tree", []))

    return EvaluationResult(rmse_in, rmse_out, corr_in, corr_out,
                            mae_in=mae_in, mae_out=mae_out, model_size=model_size)



def experiment_1(data):
    leaf_sizes = list(range(1, 15)) + list(range(15, 51, 5))
    results = []

    for i in leaf_sizes:
        r = eval_learner(data, dt.DTLearner, leaf_size=i)
        results.append([i, r.rmse_in, r.rmse_out, r.corr_in, r.corr_out])

    cs = ["leaf_size", "rmse_in", "rmse_out", "corr_in", "corr_out"]
    df = pd.DataFrame(results, columns=cs)

    x = range(len(df))  # evenly spaced x values

    plt.figure(figsize=(10, 6))
    plt.plot(x, df["rmse_in"], marker='o', label='In Sample RMSE')
    plt.plot(x, df["rmse_out"], marker='o', label='Out Sample RMSE')
    plt.title("DTLearner RMSE vs Leaf Size (Istanbul.csv data)")
    plt.xlabel("Leaf Size")
    plt.ylabel("RMSE")
    plt.xticks(ticks=x, labels=df["leaf_size"], rotation=45)
    plt.grid(True)
    plt.legend()
    plt.tight_layout()
    plt.savefig("figure_1.png")
    # print("Saved figure_1.png")


def experiment_2(data):
    """
    Evaluate the effect of bagging on overfitting relative to leaf_size.
    Uses DTLearner wrapped in BagLearner. Plots RMSE vs leaf_size.
    """

    def run_learner(leaf_size, bag_size):
        result = eval_learner(
            data,
            bl.BagLearner,
            learner=dt.DTLearner,
            bags=bag_size,
            kwargs={'leaf_size': leaf_size}
        )
        return [result.rmse_in, result.rmse_out]

    leaf_sizes = list(range(1, 15)) + list(range(15, 51, 5))
    bag_sizes = [1, 3, 5, 7, 10, 20]
    colors = plt.cm.viridis(np.linspace(0, 1, len(bag_sizes)))  # color palette

    plt.figure(figsize=(10, 6))

    for i, bag_size in enumerate(bag_sizes):
        results = []
        for leaf_size in leaf_sizes:
            rmse_in, rmse_out = run_learner(leaf_size, bag_size)
            results.append([leaf_size, rmse_in, rmse_out])

        df = pd.DataFrame(results, columns=["leaf_size", "rmse_in", "rmse_out"])
        x = df["leaf_size"]

        plt.plot(x, df["rmse_in"], linestyle='dotted', color=colors[i], label=f"In Sample (bags={bag_size})")
        plt.plot(x, df["rmse_out"], linestyle='solid', color=colors[i], label=f"Out Sample (bags={bag_size})")

    plt.title("Bagging DTLearners: RMSE vs Leaf Size (Istanbul.csv)")
    plt.xlabel("Leaf Size")
    plt.ylabel("RMSE")
    plt.gca().set_xticks(range(min(leaf_sizes), max(leaf_sizes) + 1, 1))
    plt.xticks(rotation=45)
    plt.grid(True)
    plt.legend()
    plt.tight_layout()
    plt.savefig("figure_2.png")
    # print("Saved figure_2.png")

def experiment_3(data):
    """
    Quantitatively compare "classic" decision trees (DTLearner) versus random
    trees (RTLearner). In which ways is one method better than the other?
    Provide at least two quantitative measures. Important, using two similar
    measures that illustrate the same broader metric does not count as two.
    (For example, do not use two measures for accuracy.) Note for this part of
    the report you must conduct new experiments, don't use the results of the
    experiments above for this(RMSE is not allowed as a new experiment).
    """

    def run_learner(leaf_size):
        # Evaluate DTLearner
        dt_result = eval_learner(data, dt.DTLearner, leaf_size=leaf_size)
        # Evaluate RTLearner
        rt_result = eval_learner(data, rt.RTLearner, leaf_size=leaf_size)

        return [
            dt_result.mae_in, dt_result.mae_out, dt_result.model_size,
            rt_result.mae_in, rt_result.mae_out, rt_result.model_size
        ]

    leaf_sizes = list(range(1, 51))
    results = [[ls] + run_learner(ls) for ls in leaf_sizes]

    cols = [
        "leaf_size",
        "DT_MAE_in", "DT_MAE_out", "DT_size",
        "RT_MAE_in", "RT_MAE_out", "RT_size"
    ]
    df = pd.DataFrame(results, columns=cols)

    # --- Plot 1: MAE (in and out of sample) ---
    plt.figure(figsize=(10, 6))
    plt.plot(df["leaf_size"], df["DT_MAE_out"], label="DT MAE Out", marker='o')
    plt.plot(df["leaf_size"], df["RT_MAE_out"], label="RT MAE Out", marker='o')
    plt.plot(df["leaf_size"], df["DT_MAE_in"], label="DT MAE In", linestyle="--", alpha=0.6, marker='o')
    plt.plot(df["leaf_size"], df["RT_MAE_in"], label="RT MAE In", linestyle="--", alpha=0.6, marker='o')
    plt.title("Figure 3: Mean Absolute Error vs Leaf Size")
    plt.xlabel("Leaf Size")
    plt.ylabel("MAE")
    plt.xticks(rotation=45)
    plt.grid(True)
    plt.legend()
    # Set x-ticks with step size 1 from 1 to 50
    plt.gca().set_xticks(range(1, 51, 1))
    plt.tight_layout()
    plt.savefig("figure_3.png")
    # print("Saved figure_3.png")

    # --- Plot 2: Model Size ---
    plt.figure(figsize=(10, 6))
    plt.plot(df["leaf_size"], df["DT_size"], label="DT Model Size", marker='o')
    plt.plot(df["leaf_size"], df["RT_size"], label="RT Model Size", marker='o')
    plt.title("Figure 4: Model Size vs Leaf Size")
    plt.xlabel("Leaf Size")
    plt.ylabel("Model Size (e.g., number of nodes)")
    plt.xticks(rotation=45)
    plt.grid(True)
    plt.legend()
    # Set x-ticks with step size 1 from 1 to 50
    plt.gca().set_xticks(range(1, 51, 1))
    plt.tight_layout()
    plt.savefig("figure_4.png")
    # print("Saved figure_4.png")

def gtid():  		  	   		 	 	 			  		 			 	 	 		 		 	
    """  		  	   		 	 	 			  		 			 	 	 		 		 	
    :return: The GT ID of the student  		  	   		 	 	 			  		 			 	 	 		 		 	
    :rtype: int  		  	   		 	 	 			  		 			 	 	 		 		 	
    """  		  	   		 	 	 			  		 			 	 	 		 		 	
    return 904059950


def main():
    if len(sys.argv) != 2:
        print("Usage: python testlearner.py <filename>")
        sys.exit(1)
    inf = open(sys.argv[1])
    data = np.array([list(map(float, s.strip().split(',')[1:]))
                     for s in inf.readlines()[1:]])

    # Shuffle data with GT ID as the seed
    np.random.seed(gtid())
    np.random.shuffle(data)

    # compute how much of the data is training and testing
    train_rows = int(0.6 * data.shape[0])
    test_rows = data.shape[0] - train_rows

    # separate out training and testing data
    train_x = data[:train_rows, 0:-1]
    train_y = data[:train_rows, -1]
    test_x = data[train_rows:, 0:-1]
    test_y = data[train_rows:, -1]

    print(f"{test_x.shape}")
    print(f"{test_y.shape}")

    # Define test function inside main
    def test_learner_inline(learner_class, **kwargs):
        print(f"\nResults for {learner_class.__name__}:")
        learner = learner_class(**kwargs)
        learner.add_evidence(train_x, train_y)
        print(learner.author())

        # evaluate in sample
        pred_y = learner.query(train_x)
        rmse = math.sqrt(((train_y - pred_y) ** 2).sum() / train_y.shape[0])
        corr = np.corrcoef(pred_y, y=train_y)[0, 1]
        print(f"  In sample RMSE: {rmse}")
        print(f"  In sample correlation: {corr}")

        # evaluate out of sample
        pred_y = learner.query(test_x)
        rmse = math.sqrt(((test_y - pred_y) ** 2).sum() / test_y.shape[0])
        corr = np.corrcoef(pred_y, y=test_y)[0, 1]
        print(f"  Out of sample RMSE: {rmse}")
        print(f"  Out of sample correlation: {corr}")

    # Test the LinRegLearner
    test_learner_inline(lrl.LinRegLearner, verbose=True)

    # Run experiments
    data_tuple = (train_x, train_y, test_x, test_y)
    experiment_1(data_tuple)
    experiment_2(data_tuple)
    experiment_3(data_tuple)


if __name__=="__main__":
    main()  		  	   		 	 	 			  		 			 	 	 		 		 	
