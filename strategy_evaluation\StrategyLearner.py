""""""  		  	   		 	 	 			  		 			 	 	 		 		 	
"""  		  	   		 	 	 			  		 			 	 	 		 		 	
Template for implementing StrategyLearner  (c) 2016 Tucker Balch  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
Copyright 2018, Georgia Institute of Technology (Georgia Tech)  		  	   		 	 	 			  		 			 	 	 		 		 	
Atlanta, Georgia 30332  		  	   		 	 	 			  		 			 	 	 		 		 	
All Rights Reserved  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
Template code for CS 4646/7646  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
Georgia Tech asserts copyright ownership of this template and all derivative  		  	   		 	 	 			  		 			 	 	 		 		 	
works, including solutions to the projects assigned in this course. Students  		  	   		 	 	 			  		 			 	 	 		 		 	
and other users of this template code are advised not to share it with others  		  	   		 	 	 			  		 			 	 	 		 		 	
or to make it available on publicly viewable websites including repositories  		  	   		 	 	 			  		 			 	 	 		 		 	
such as github and gitlab.  This copyright statement should not be removed  		  	   		 	 	 			  		 			 	 	 		 		 	
or edited.  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
We do grant permission to share solutions privately with non-students such  		  	   		 	 	 			  		 			 	 	 		 		 	
as potential employers. However, sharing with other current or future  		  	   		 	 	 			  		 			 	 	 		 		 	
students of CS 7646 is prohibited and subject to being investigated as a  		  	   		 	 	 			  		 			 	 	 		 		 	
GT honor code violation.  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
-----do not edit anything above this line---  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
Student Name: <PERSON> (replace with your name)  		  	   		 	 	 			  		 			 	 	 		 		 	
GT User ID: jjang333 (replace with your User ID)  		  	   		 	 	 			  		 			 	 	 		 		 	
GT ID: 904059950 (replace with your GT ID)  		  	   		 	 	 			  		 			 	 	 		 		 	
"""  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
import datetime as dt
import random
import numpy as np

import pandas as pd
import util as ut
from QLearner import QLearner
from indicators import BBP, SMA_ratio, RSI
  		  	   		 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
class StrategyLearner(object):
    """
    A strategy learner that can learn a trading policy using the same indicators used in ManualStrategy.

    :param verbose: If “verbose” is True, your code can print out information for debugging.
        If verbose = False your code should not generate ANY output.
    :type verbose: bool
    :param impact: The market impact of each transaction, defaults to 0.0
    :type impact: float
    :param commission: The commission amount charged, defaults to 0.0
    :type commission: float
    """

    # constructor
    def __init__(self, verbose=False, impact=0.0, commission=0.0):
        """
        Constructor method
        """
        self.verbose = verbose
        self.impact = impact
        self.commission = commission
        self.num_states = 3000  # 1000 indicator states * 3 position states
        self.num_actions = 3  # 0=SHORT, 1=CASH, 2=LONG
        self.learner = None  # Will be initialized in add_evidence
        self.trained = False

    # this method should create a QLearner, and train it for trading
    def add_evidence(self, symbol="IBM", sd=dt.datetime(2008, 1, 1), ed=dt.datetime(2009, 1, 1), sv=10000):
        """
        Trains your strategy learner over a given time frame.
        """
        # Symbol-specific Q-Learning parameters
        if 'SINE_FAST_NOISE' in symbol:
            alpha, gamma, rar, radr = 0.3, 0.95, 0.8, 0.995
            epochs = 200
            reward_scale = 1000
        elif 'ML4T' in symbol:
            alpha, gamma, rar, radr = 0.2, 0.9, 0.5, 0.99
            epochs = 100
            reward_scale = 100
        else:
            alpha, gamma, rar, radr = 0.2, 0.9, 0.5, 0.99
            epochs = 100
            reward_scale = 100

        # Initialize Q-Learner with symbol-specific parameters
        self.learner = QLearner(
            num_states=self.num_states,
            num_actions=self.num_actions,
            alpha=alpha,
            gamma=gamma,
            rar=rar,
            radr=radr,
            dyna=0,
            verbose=False
        )

        # Get price data
        dates = pd.date_range(start=sd, end=ed)
        prices_all = ut.get_data([symbol], dates)
        prices = prices_all[symbol]

        # Calculate indicators
        bbp_values = BBP(prices)
        sma_ratio_values = SMA_ratio(prices)
        rsi_values = RSI(prices)

        # Combine indicators and normalize
        features = pd.DataFrame({
            'BBP': bbp_values,
            'SMA_Ratio': sma_ratio_values,
            'RSI': rsi_values,
        }).dropna()

        # Normalize features
        features = (features - features.mean()) / features.std()

        # Discretize features into states (without position for now)
        states = self.discretize_features(features)

        # Align prices with states
        aligned_prices = prices.loc[states.index]

        # Training parameters (epochs and reward_scale set above)
        N = 5  # N-day future return for reward

        # Training loop with better reward structure
        for epoch in range(epochs):
            position = 0  # Current position: -1000, 0, 1000

            # Initialize first state
            if len(states) > 0:
                # Create state that includes position
                state_with_position = states.iloc[0] * 3 + 1  # position=0 maps to +1
                action = self.learner.querysetstate(int(state_with_position))

            for i in range(1, len(states) - N):
                # Convert action to new position
                if action == 0:  # SHORT
                    new_position = -1000
                elif action == 1:  # CASH
                    new_position = 0
                else:  # LONG (action == 2)
                    new_position = 1000

                # Calculate reward based on holding the position for N days
                future_return = (aligned_prices.iloc[i + N] - aligned_prices.iloc[i]) / aligned_prices.iloc[i]

                # Reward based on position profitability
                if 'SINE_FAST_NOISE' in symbol:
                    # Special handling for SINE_FAST_NOISE
                    if new_position > 0:  # Long position
                        reward = -future_return * reward_scale  # Inverted logic
                    elif new_position < 0:  # Short position
                        reward = future_return * reward_scale   # Inverted logic
                    else:  # Cash position
                        reward = 0
                else:
                    # Standard logic for other symbols
                    if new_position > 0:  # Long position
                        reward = future_return * reward_scale
                    elif new_position < 0:  # Short position
                        reward = -future_return * reward_scale
                    else:  # Cash position
                        reward = 0

                # Apply transaction cost only when position changes
                if new_position != position:
                    reward -= 10.0  # Fixed transaction cost

                # Update position
                position = new_position

                # Create next state that includes position
                position_code = 0 if position == -1000 else (1 if position == 0 else 2)
                next_state_with_position = states.iloc[i] * 3 + position_code
                action = self.learner.query(int(next_state_with_position), reward)

        self.trained = True

        """  		  	   		 	   			  		 			     			  	 
        # example usage of the old backward compatible util function  		  	   		 	   			  		 			     			  	 
        syms = [symbol]  		  	   		 	   			  		 			     			  	 
        dates = pd.date_range(sd, ed)  		  	   		 	   			  		 			     			  	 
        prices_all = ut.get_data(syms, dates)  # automatically adds SPY  		  	   		 	   			  		 			     			  	 
        prices = prices_all[syms]  # only portfolio symbols  		  	   		 	   			  		 			     			  	 
        prices_SPY = prices_all["SPY"]  # only SPY, for comparison later  		  	   		 	   			  		 			     			  	 
        if self.verbose:  		  	   		 	   			  		 			     			  	 
            print(prices)  		  	   		 	   			  		 			     			  	 

        # example use with new colname  		  	   		 	   			  		 			     			  	 
        volume_all = ut.get_data(  		  	   		 	   			  		 			     			  	 
            syms, dates, colname="Volume"  		  	   		 	   			  		 			     			  	 
        )  # automatically adds SPY  		  	   		 	   			  		 			     			  	 
        volume = volume_all[syms]  # only portfolio symbols  		  	   		 	   			  		 			     			  	 
        volume_SPY = volume_all["SPY"]  # only SPY, for comparison later  		  	   		 	   			  		 			     			  	 
        if self.verbose:  		  	   		 	   			  		 			     			  	 
            print(volume)  		
        """

    def discretize_features(self, features, position_series=None):
        """
        Discretizes continuous feature values into discrete states using quantile-based binning
        Optionally includes position information in the state
        """
        num_bins = 10
        discretized = pd.DataFrame(index=features.index)

        # Discretize each feature column separately
        for col in features.columns:
            try:
                discretized[col], _ = pd.qcut(features[col], q=num_bins, labels=False, retbins=True, duplicates="drop")
            except ValueError:
                # If qcut fails (e.g., too many duplicates), use simple binning
                discretized[col] = pd.cut(features[col], bins=num_bins, labels=False)

        # Add position state if provided
        if position_series is not None:
            # Convert position to discrete state: -1000->0, 0->1, 1000->2
            position_state = position_series.map({-1000: 0, 0: 1, 1000: 2}).fillna(1)
            discretized['position'] = position_state

        # Combine all features into a single state
        # Convert to a single state number by treating it as a base-num_bins number
        state_multiplier = 1
        combined_state = pd.Series(0, index=features.index)

        for col in discretized.columns:
            if col == 'position':
                combined_state += discretized[col].fillna(1) * state_multiplier
                state_multiplier *= 3  # 3 position states
            else:
                combined_state += discretized[col].fillna(0) * state_multiplier
                state_multiplier *= num_bins

        return combined_state.astype(int)

    # this method should use the existing policy and test it against new data
    def testPolicy(self, symbol="IBM", sd=dt.datetime(2009, 1, 1), ed=dt.datetime(2010, 1, 1), sv=10000):
        """
        Tests your learner using data outside of the training data
        """
        if not self.trained:
            raise ValueError("Strategy learner has not been trained yet!")

        # Get price data
        dates = pd.date_range(sd, ed)
        prices_all = ut.get_data([symbol], dates)
        prices = prices_all[symbol]

        # Calculate same indicators as training
        bbp_values = BBP(prices)
        sma_ratio_values = SMA_ratio(prices)
        rsi_values = RSI(prices)

        # Combine and normalize features (same as training)
        features = pd.DataFrame({
            'BBP': bbp_values,
            'SMA_Ratio': sma_ratio_values,
            'RSI': rsi_values,
        }).dropna()

        features = (features - features.mean()) / features.std()

        # Discretize features
        states = self.discretize_features(features)

        # Initialize trades DataFrame
        trades = prices_all[[symbol]].copy()
        trades.values[:, :] = 0

        # Test policy (no learning) - with position-aware states
        position = 0  # Current position

        for i in range(len(states)):
            # Create state that includes position (same encoding as training)
            position_code = 0 if position == -1000 else (1 if position == 0 else 2)
            current_state_with_position = states.iloc[i] * 3 + position_code

            # Query learner for action (no learning)
            action = self.learner.querysetstate(int(current_state_with_position))

            # Convert action to target position
            if action == 0:  # SHORT
                target_position = -1000
            elif action == 1:  # CASH
                target_position = 0
            else:  # LONG (action == 2)
                target_position = 1000

            # Calculate trade needed
            trade = target_position - position

            # Record trade if needed
            if trade != 0:
                # Find the corresponding date in the original trades DataFrame
                state_date = states.index[i]
                if state_date in trades.index:
                    trades.loc[state_date, symbol] = trade
                    position = target_position

        return trades

    def author(self):
        return 'jjang333'

    def study_group(self):
        return 'jjang333'  	 	 			  		 			 	 	 		 		 	 	 	 			  		 			 	 	 		 		 	
  		  	   		 	 	 			  		 			 	 	 		 		 	
if __name__ == "__main__":  		  	   		 	 	 			  		 			 	 	 		 		 	
    print("One does not simply think up a strategy")  		  	   		 	 	 			  		 			 	 	 		 		 	
