    def add_evidence(self, symbol="IBM", sd=dt.datetime(2008, 1, 1), ed=dt.datetime(2009, 1, 1), sv=10000):
        """
        Trains your strategy learner over a given time frame following Q-Trader methodology.
        """
        # Store symbol for adaptive parameters
        self._current_symbol = symbol
        
        # Reinitialize learner with optimized parameters
        self.learner = QLearner(
            num_states=self.num_states,
            num_actions=self.num_actions,
            alpha=0.2,
            gamma=0.9,
            rar=0.5,
            radr=0.99,
            dyna=0,
            verbose=False
        )

        # Get price data
        dates = pd.date_range(sd, ed)
        price_df = ut.get_data([symbol], dates, addSPY=True, colname='Adj Close')
        price_df = price_df.drop(['SPY'], axis=1, errors='ignore')

        # Get indicators
        indicators_df = get_all_indicators(price_df, symbol)
        prices = price_df[symbol].values

        # Training parameters following Q-Trader hints
        N = 5  # N-day future return for reward calculation
        epochs = 100  # Multiple training iterations for convergence
        
        if self.verbose:
            print(f"Training QLearner for {symbol} from {sd} to {ed}")
            print(f"Data shape: {price_df.shape}")

        # Training loop - multiple epochs for convergence
        for epoch in range(epochs):
            position = 0  # Start with no position: -1000=SHORT, 0=CASH, 1000=LONG
            
            # Initialize first state
            if len(indicators_df) > 0:
                current_state = int(self.discretize_indicators(indicators_df.iloc[[0]], position))
                action = self.learner.querysetstate(current_state)
            
            # Step through each day in training data
            for i in range(1, len(prices) - N):
                # Convert action to position: 0=SHORT(-1000), 1=CASH(0), 2=LONG(1000)
                if action == 0:
                    new_position = -1000
                elif action == 1:
                    new_position = 0
                else:  # action == 2
                    new_position = 1000
                
                # Calculate reward based on N-day future return (Q-Trader methodology)
                future_price = prices[i + N] if i + N < len(prices) else prices[-1]
                current_price = prices[i]
                future_return = (future_price / current_price) - 1.0
                
                # Reward based on position profitability
                if new_position > 0:  # Long position
                    reward = future_return * 100  # Profit when price goes up
                elif new_position < 0:  # Short position
                    reward = -future_return * 100  # Profit when price goes down
                else:  # Cash position
                    reward = 0  # No market exposure
                
                # Transaction cost penalty for position changes
                if new_position != position:
                    reward -= 1.0
                
                # Update position
                position = new_position
                
                # Calculate next state and query learner
                next_state = int(self.discretize_indicators(indicators_df.iloc[[i]], position))
                action = self.learner.query(next_state, reward)

        self.trained = True
        if self.verbose:
            print("Training completed!")
