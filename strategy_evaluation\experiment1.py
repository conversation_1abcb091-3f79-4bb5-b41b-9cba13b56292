"""
Experiment 1: Compare Manual Strategy vs Strategy Learner
"""
import matplotlib.pyplot as plt
import pandas as pd
import datetime as dt

from ManualStrategy import ManualStrategy
from StrategyLearner import StrategyLearner
from marketsimcode import compute_portvals
from util import get_data


# Helper functions removed - using StrategyLearner trades directly


def run_experiment1():
    start_date = dt.datetime(2008, 1, 1)
    end_date = dt.datetime(2009, 12, 31)
    starting_value = 100000
    impt = 0.005
    comm = 9.95
    ticker = "JPM"

    # Get benchmark data
    dates = pd.date_range(start_date, end_date)
    prices = get_data([ticker], dates, addSPY=True, colname="Adj Close")

    # Create benchmark portfolio (buy and hold)
    benchmark_trades = pd.DataFrame(data=0, index=prices.index, columns=[ticker])
    benchmark_trades.iloc[0] = 1000  # Buy 1000 shares on first day
    benchmark_portval = compute_portvals(benchmark_trades, start_val=starting_value, commission=comm, impact=impt)
    benchmark_portval = benchmark_portval / benchmark_portval.iloc[0]

    # Manual Strategy
    manual = ManualStrategy()
    manual_trades = manual.testPolicy(symbol=ticker, sd=start_date, ed=end_date, sv=starting_value)
    ms_port_value = compute_portvals(manual_trades, start_val=starting_value, commission=comm, impact=impt)
    ms_port_value = ms_port_value / ms_port_value.iloc[0]

    # Strategy Learner
    learner = StrategyLearner(impact=impt, commission=comm)
    learner.add_evidence(symbol=ticker, sd=start_date, ed=end_date, sv=starting_value)
    sl_trades = learner.testPolicy(symbol=ticker, sd=start_date, ed=end_date, sv=starting_value)
    sl_port_value = compute_portvals(sl_trades, start_val=starting_value, commission=comm, impact=impt)
    sl_port_value = sl_port_value / sl_port_value.iloc[0]

    # Create comparison chart
    fig = plt.figure(figsize=(12, 8))
    plt.plot(benchmark_portval.index, benchmark_portval.values, 'g-', label='Benchmark')
    plt.plot(ms_port_value.index, ms_port_value.values, 'r-', label='Manual Strategy')
    plt.plot(sl_port_value.index, sl_port_value.values, 'b-', label='Strategy Learner')
    plt.legend()
    plt.xlabel("Date")
    plt.ylabel("Normalized Portfolio Value")
    plt.title("Experiment 1: Strategy Comparison")
    plt.grid(True)
    fig.autofmt_xdate()
    plt.savefig("experiment1_comparison.png", dpi=300, bbox_inches='tight')
    plt.close()

    return {
        'benchmark': benchmark_portval,
        'manual': ms_port_value,
        'learner': sl_port_value
    }

def author():
    return 'jjang333'

if __name__ == "__main__":
    run_experiment1()