"""
Experiment 1: Compare Manual Strategy vs Strategy Learner
"""
import matplotlib.pyplot as plt
import pandas as pd
import datetime as dt

from ManualStrategy import ManualStrategy
from StrategyLearner import StrategyLearner
from marketsimcode import compute_portvals
from util import get_data


# Helper functions removed - using StrategyLearner trades directly


def run_experiment1():
    # Date ranges
    in_start_date = dt.datetime(2008, 1, 1)
    in_end_date = dt.datetime(2009, 12, 31)
    out_start_date = dt.datetime(2010, 1, 1)
    out_end_date = dt.datetime(2011, 12, 31)
    starting_value = 100000
    impt = 0.005
    comm = 9.95
    ticker = "JPM"

    print("Running Experiment 1: Manual Strategy vs Strategy Learner Comparison")
    print("="*70)

    # ========================================
    # IN-SAMPLE ANALYSIS (2008-2009)
    # ========================================
    print("Analyzing in-sample period (2008-2009)...")

    # Get in-sample data
    in_dates = pd.date_range(in_start_date, in_end_date)
    in_prices = get_data([ticker], in_dates, addSPY=True, colname="Adj Close")

    # Create benchmark portfolio (buy and hold)
    in_benchmark_trades = pd.DataFrame(data=0, index=in_prices.index, columns=[ticker])
    in_benchmark_trades.iloc[0] = 1000  # Buy 1000 shares on first day
    in_benchmark_portval = compute_portvals(in_benchmark_trades, start_val=starting_value, commission=comm, impact=impt)
    in_benchmark_portval = in_benchmark_portval / in_benchmark_portval.iloc[0]

    # Manual Strategy
    manual = ManualStrategy(impact=impt, commission=comm)
    in_manual_trades = manual.testPolicy(symbol=ticker, sd=in_start_date, ed=in_end_date, sv=starting_value)
    in_ms_port_value = compute_portvals(in_manual_trades, start_val=starting_value, commission=comm, impact=impt)
    in_ms_port_value = in_ms_port_value / in_ms_port_value.iloc[0]

    # Strategy Learner (train on in-sample data)
    learner = StrategyLearner(impact=impt, commission=comm)
    learner.add_evidence(symbol=ticker, sd=in_start_date, ed=in_end_date, sv=starting_value)
    in_sl_trades = learner.testPolicy(symbol=ticker, sd=in_start_date, ed=in_end_date, sv=starting_value)
    in_sl_port_value = compute_portvals(in_sl_trades, start_val=starting_value, commission=comm, impact=impt)
    in_sl_port_value = in_sl_port_value / in_sl_port_value.iloc[0]

    # Create in-sample comparison chart
    fig = plt.figure(figsize=(12, 8))
    plt.plot(in_benchmark_portval.index, in_benchmark_portval.values, 'purple', label='Benchmark', linewidth=2)
    plt.plot(in_ms_port_value.index, in_ms_port_value.values, 'red', label='Manual Strategy', linewidth=2)
    plt.plot(in_sl_port_value.index, in_sl_port_value.values, 'blue', label='Strategy Learner', linewidth=2)
    plt.legend()
    plt.xlabel("Date")
    plt.ylabel("Normalized Portfolio Value")
    plt.title("Experiment 1: In-Sample Strategy Comparison (2008-2009)")
    plt.grid(True, alpha=0.3)
    fig.autofmt_xdate()
    plt.savefig("experiment1_in_sample.png", dpi=300, bbox_inches='tight')
    plt.close()

    # ========================================
    # OUT-OF-SAMPLE ANALYSIS (2010-2011)
    # ========================================
    print("Analyzing out-of-sample period (2010-2011)...")

    # Get out-of-sample data
    out_dates = pd.date_range(out_start_date, out_end_date)
    out_prices = get_data([ticker], out_dates, addSPY=True, colname="Adj Close")

    # Create benchmark portfolio (buy and hold)
    out_benchmark_trades = pd.DataFrame(data=0, index=out_prices.index, columns=[ticker])
    out_benchmark_trades.iloc[0] = 1000  # Buy 1000 shares on first day
    out_benchmark_portval = compute_portvals(out_benchmark_trades, start_val=starting_value, commission=comm, impact=impt)
    out_benchmark_portval = out_benchmark_portval / out_benchmark_portval.iloc[0]

    # Manual Strategy (test on out-of-sample data)
    out_manual_trades = manual.testPolicy(symbol=ticker, sd=out_start_date, ed=out_end_date, sv=starting_value)
    out_ms_port_value = compute_portvals(out_manual_trades, start_val=starting_value, commission=comm, impact=impt)
    out_ms_port_value = out_ms_port_value / out_ms_port_value.iloc[0]

    # Strategy Learner (test on out-of-sample data using trained model)
    out_sl_trades = learner.testPolicy(symbol=ticker, sd=out_start_date, ed=out_end_date, sv=starting_value)
    out_sl_port_value = compute_portvals(out_sl_trades, start_val=starting_value, commission=comm, impact=impt)
    out_sl_port_value = out_sl_port_value / out_sl_port_value.iloc[0]

    # Create out-of-sample comparison chart
    fig = plt.figure(figsize=(12, 8))
    plt.plot(out_benchmark_portval.index, out_benchmark_portval.values, 'purple', label='Benchmark', linewidth=2)
    plt.plot(out_ms_port_value.index, out_ms_port_value.values, 'red', label='Manual Strategy', linewidth=2)
    plt.plot(out_sl_port_value.index, out_sl_port_value.values, 'blue', label='Strategy Learner', linewidth=2)
    plt.legend()
    plt.xlabel("Date")
    plt.ylabel("Normalized Portfolio Value")
    plt.title("Experiment 1: Out-of-Sample Strategy Comparison (2010-2011)")
    plt.grid(True, alpha=0.3)
    fig.autofmt_xdate()
    plt.savefig("experiment1_out_of_sample.png", dpi=300, bbox_inches='tight')
    plt.close()

    # ========================================
    # PERFORMANCE SUMMARY
    # ========================================
    print("\nPerformance Summary:")
    print("-" * 50)

    # Calculate final returns
    in_bench_return = (in_benchmark_portval.iloc[-1] - 1) * 100
    in_manual_return = (in_ms_port_value.iloc[-1] - 1) * 100
    in_learner_return = (in_sl_port_value.iloc[-1] - 1) * 100

    out_bench_return = (out_benchmark_portval.iloc[-1] - 1) * 100
    out_manual_return = (out_ms_port_value.iloc[-1] - 1) * 100
    out_learner_return = (out_sl_port_value.iloc[-1] - 1) * 100

    print("IN-SAMPLE (2008-2009):")
    print(f"  Benchmark:       {in_bench_return:.2f}%")
    print(f"  Manual Strategy: {in_manual_return:.2f}%")
    print(f"  Strategy Learner: {in_learner_return:.2f}%")

    print("\nOUT-OF-SAMPLE (2010-2011):")
    print(f"  Benchmark:       {out_bench_return:.2f}%")
    print(f"  Manual Strategy: {out_manual_return:.2f}%")
    print(f"  Strategy Learner: {out_learner_return:.2f}%")

    print("\nCharts generated:")
    print("  - experiment1_in_sample.png")
    print("  - experiment1_out_of_sample.png")
    print("\nExperiment 1 completed successfully!")

    return {
        'in_sample': {
            'benchmark': in_benchmark_portval,
            'manual': in_ms_port_value,
            'learner': in_sl_port_value
        },
        'out_of_sample': {
            'benchmark': out_benchmark_portval,
            'manual': out_ms_port_value,
            'learner': out_sl_port_value
        }
    }

def author():
    return 'jjang333'

if __name__ == "__main__":
    run_experiment1()