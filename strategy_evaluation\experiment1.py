"""
Experiment 1: Compare Manual Strategy vs Strategy Learner
"""
import numpy as np
import datetime as dt
import matplotlib.pyplot as plt
from ManualStrategy import ManualStrategy
from StrategyLearner import StrategyLearner
import marketsimcode as ms

def run_experiment1():
    """
    Compare the performance of Manual Strategy vs Strategy Learner
    """
    symbol = "JPM"
    start_val = 100000
    commission = 9.95
    impact = 0.005
    
    # Date ranges
    in_sample_sd = dt.datetime(2008, 1, 1)
    in_sample_ed = dt.datetime(2009, 12, 31)
    out_sample_sd = dt.datetime(2010, 1, 1)
    out_sample_ed = dt.datetime(2011, 12, 31)
    
    # Initialize strategies
    manual_strategy = ManualStrategy(verbose=True, impact=impact, commission=commission)
    strategy_learner = StrategyLearner(verbose=True, impact=impact, commission=commission)
    
    # Train the strategy learner
    print("Training Strategy Learner...")
    strategy_learner.add_evidence(symbol=symbol, sd=in_sample_sd, ed=in_sample_ed, sv=start_val)
    
    # Get trades for in-sample period
    print("\nGenerating in-sample trades...")
    manual_trades_in = manual_strategy.testPolicy(symbol=symbol, sd=in_sample_sd, ed=in_sample_ed, sv=start_val)
    learner_trades_in = strategy_learner.testPolicy(symbol=symbol, sd=in_sample_sd, ed=in_sample_ed, sv=start_val)
    benchmark_trades_in = manual_strategy.benchmark_strategy(symbol=symbol, sd=in_sample_sd, ed=in_sample_ed, sv=start_val)
    
    # Get trades for out-sample period
    print("\nGenerating out-of-sample trades...")
    manual_trades_out = manual_strategy.testPolicy(symbol=symbol, sd=out_sample_sd, ed=out_sample_ed, sv=start_val)
    learner_trades_out = strategy_learner.testPolicy(symbol=symbol, sd=out_sample_sd, ed=out_sample_ed, sv=start_val)
    benchmark_trades_out = manual_strategy.benchmark_strategy(symbol=symbol, sd=out_sample_sd, ed=out_sample_ed, sv=start_val)
    
    # Calculate portfolio values
    print("\nCalculating portfolio values...")
    manual_portvals_in = ms.compute_portvals(manual_trades_in, start_val=start_val, commission=commission, impact=impact)
    learner_portvals_in = ms.compute_portvals(learner_trades_in, start_val=start_val, commission=commission, impact=impact)
    benchmark_portvals_in = ms.compute_portvals(benchmark_trades_in, start_val=start_val, commission=commission, impact=impact)
    
    manual_portvals_out = ms.compute_portvals(manual_trades_out, start_val=start_val, commission=commission, impact=impact)
    learner_portvals_out = ms.compute_portvals(learner_trades_out, start_val=start_val, commission=commission, impact=impact)
    benchmark_portvals_out = ms.compute_portvals(benchmark_trades_out, start_val=start_val, commission=commission, impact=impact)
    
    # Normalize portfolio values
    manual_portvals_in_norm = manual_portvals_in / manual_portvals_in.iloc[0]
    learner_portvals_in_norm = learner_portvals_in / learner_portvals_in.iloc[0]
    benchmark_portvals_in_norm = benchmark_portvals_in / benchmark_portvals_in.iloc[0]
    
    manual_portvals_out_norm = manual_portvals_out / manual_portvals_out.iloc[0]
    learner_portvals_out_norm = learner_portvals_out / learner_portvals_out.iloc[0]
    benchmark_portvals_out_norm = benchmark_portvals_out / benchmark_portvals_out.iloc[0]
    
    # Create a combined subplot figure for saving (NO DISPLAY, ONLY SAVE)
    print("Generating Experiment 1 comparison chart...")
    plt.figure(figsize=(14, 10))

    plt.subplot(2, 1, 1)
    plt.plot(benchmark_portvals_in_norm.index, benchmark_portvals_in_norm.values, 'purple', label='Benchmark', linewidth=2)
    plt.plot(manual_portvals_in_norm.index, manual_portvals_in_norm.values, 'red', label='Manual Strategy', linewidth=2)
    plt.plot(learner_portvals_in_norm.index, learner_portvals_in_norm.values, 'green', label='Strategy Learner', linewidth=2)
    plt.title('In-Sample Performance Comparison (2008-2009)')
    plt.xlabel('Date')
    plt.ylabel('Normalized Portfolio Value')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.subplot(2, 1, 2)
    plt.plot(benchmark_portvals_out_norm.index, benchmark_portvals_out_norm.values, 'purple', label='Benchmark', linewidth=2)
    plt.plot(manual_portvals_out_norm.index, manual_portvals_out_norm.values, 'red', label='Manual Strategy', linewidth=2)
    plt.plot(learner_portvals_out_norm.index, learner_portvals_out_norm.values, 'green', label='Strategy Learner', linewidth=2)
    plt.title('Out-of-Sample Performance Comparison (2010-2011)')
    plt.xlabel('Date')
    plt.ylabel('Normalized Portfolio Value')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('experiment1_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()  # Close the figure to avoid showing it twice
    
    # Calculate and display performance statistics
    def get_stats(portvals):
        daily_returns = portvals.pct_change().dropna()
        return {
            'Cumulative Return': (portvals.iloc[-1] / portvals.iloc[0]) - 1,
            'Mean Daily Return': daily_returns.mean(),
            'Std Daily Return': daily_returns.std(),
            'Sharpe Ratio': (daily_returns.mean() / daily_returns.std()) * np.sqrt(252)
        }
    
    # Performance statistics
    print("\n" + "="*60)
    print("EXPERIMENT 1 RESULTS: MANUAL STRATEGY vs STRATEGY LEARNER")
    print("="*60)
    
    # In-sample statistics
    manual_stats_in = get_stats(manual_portvals_in)
    learner_stats_in = get_stats(learner_portvals_in)
    benchmark_stats_in = get_stats(benchmark_portvals_in)
    
    print("\nIN-SAMPLE PERFORMANCE (2008-2009):")
    print("-" * 50)
    print(f"{'Metric':<20} {'Benchmark':<12} {'Manual':<12} {'Learner':<12}")
    print("-" * 50)
    print(f"{'Cumulative Return':<20} {benchmark_stats_in['Cumulative Return']:<12.4f} {manual_stats_in['Cumulative Return']:<12.4f} {learner_stats_in['Cumulative Return']:<12.4f}")
    print(f"{'Mean Daily Return':<20} {benchmark_stats_in['Mean Daily Return']:<12.6f} {manual_stats_in['Mean Daily Return']:<12.6f} {learner_stats_in['Mean Daily Return']:<12.6f}")
    print(f"{'Std Daily Return':<20} {benchmark_stats_in['Std Daily Return']:<12.6f} {manual_stats_in['Std Daily Return']:<12.6f} {learner_stats_in['Std Daily Return']:<12.6f}")
    print(f"{'Sharpe Ratio':<20} {benchmark_stats_in['Sharpe Ratio']:<12.4f} {manual_stats_in['Sharpe Ratio']:<12.4f} {learner_stats_in['Sharpe Ratio']:<12.4f}")
    
    # Out-of-sample statistics
    manual_stats_out = get_stats(manual_portvals_out)
    learner_stats_out = get_stats(learner_portvals_out)
    benchmark_stats_out = get_stats(benchmark_portvals_out)
    
    print("\nOUT-OF-SAMPLE PERFORMANCE (2010-2011):")
    print("-" * 50)
    print(f"{'Metric':<20} {'Benchmark':<12} {'Manual':<12} {'Learner':<12}")
    print("-" * 50)
    print(f"{'Cumulative Return':<20} {benchmark_stats_out['Cumulative Return']:<12.4f} {manual_stats_out['Cumulative Return']:<12.4f} {learner_stats_out['Cumulative Return']:<12.4f}")
    print(f"{'Mean Daily Return':<20} {benchmark_stats_out['Mean Daily Return']:<12.6f} {manual_stats_out['Mean Daily Return']:<12.6f} {learner_stats_out['Mean Daily Return']:<12.6f}")
    print(f"{'Std Daily Return':<20} {benchmark_stats_out['Std Daily Return']:<12.6f} {manual_stats_out['Std Daily Return']:<12.6f} {learner_stats_out['Std Daily Return']:<12.6f}")
    print(f"{'Sharpe Ratio':<20} {benchmark_stats_out['Sharpe Ratio']:<12.4f} {manual_stats_out['Sharpe Ratio']:<12.4f} {learner_stats_out['Sharpe Ratio']:<12.4f}")
    
    # Trade count analysis
    print("\nTRADING ACTIVITY ANALYSIS:")
    print("-" * 50)
    manual_trades_in_count = (manual_trades_in != 0).sum().sum()
    learner_trades_in_count = (learner_trades_in != 0).sum().sum()
    manual_trades_out_count = (manual_trades_out != 0).sum().sum()
    learner_trades_out_count = (learner_trades_out != 0).sum().sum()
    
    print(f"Manual Strategy trades (In-sample): {manual_trades_in_count}")
    print(f"Strategy Learner trades (In-sample): {learner_trades_in_count}")
    print(f"Manual Strategy trades (Out-of-sample): {manual_trades_out_count}")
    print(f"Strategy Learner trades (Out-of-sample): {learner_trades_out_count}")
    
    # Summary observations
    print("\nKEY OBSERVATIONS:")
    print("-" * 50)
    
    if manual_stats_in['Cumulative Return'] > learner_stats_in['Cumulative Return']:
        print("• Manual Strategy outperformed Strategy Learner in-sample")
    else:
        print("• Strategy Learner outperformed Manual Strategy in-sample")
    
    if manual_stats_out['Cumulative Return'] > learner_stats_out['Cumulative Return']:
        print("• Manual Strategy outperformed Strategy Learner out-of-sample")
    else:
        print("• Strategy Learner outperformed Manual Strategy out-of-sample")
    
    if learner_stats_in['Cumulative Return'] > learner_stats_out['Cumulative Return']:
        print("• Strategy Learner performance degraded from in-sample to out-of-sample (potential overfitting)")
    else:
        print("• Strategy Learner maintained or improved performance out-of-sample")
    
    print("\n" + "="*60)

def author():
    return 'jjang333'

if __name__ == "__main__":
    run_experiment1()