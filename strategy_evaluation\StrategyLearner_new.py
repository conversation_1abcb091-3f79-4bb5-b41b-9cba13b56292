"""
Template for implementing StrategyLearner (c) 2016 Tucker Balch
Copyright 2018, Georgia Institute of Technology (Georgia Tech)
Atlanta, Georgia 30332
All Rights Reserved

Template code for CS 4646/7646

Georgia Tech asserts copyright ownership of this template and all derivative
works, including solutions to the projects assigned in this course. Students
and other users of this template code are advised not to share it with others
or to make it available on publicly viewable websites including repositories
such as github and gitlab. This copyright statement should not be removed
or edited.

We do grant permission to share solutions privately with non-students such
as potential employers. However, sharing with other current or future
students of CS 7646 is prohibited and subject to being investigated as a
GT honor code violation.

-----do not edit anything above this line---

Student Name: <PERSON> (replace with your name)
GT User ID: jjang333 (replace with your User ID)
GT ID: 904059950 (replace with your GT ID)
"""

import datetime as dt
import pandas as pd
import numpy as np
import util as ut
from indicators import get_all_indicators
from QLearner import QLearner

def author():
    """
    :return: The GT username of the student
    :rtype: str
    """
    return "jjang333"

class StrategyLearner(object):
    """
    A strategy learner that can learn a trading policy using Q-learning.
    """

    def __init__(self, verbose=False, impact=0.0, commission=0.0):
        """
        Constructor method
        """
        self.verbose = verbose
        self.impact = impact
        self.commission = commission
        self.trained = False
        
        # Q-Learning parameters following Q-Trader hints
        self.bins = 10  # Number of bins for each indicator
        self.num_states = (self.bins ** 3) * 3  # 10^3 * 3 holding states = 3000 states
        self.num_actions = 3    # 0=SHORT, 1=CASH, 2=LONG (positions)
        
        # Initialize Q-Learner
        self.learner = QLearner(
            num_states=self.num_states,
            num_actions=self.num_actions,
            alpha=0.2,
            gamma=0.9,
            rar=0.5,
            radr=0.99,
            dyna=0,
            verbose=False
        )

    def discretize_indicators(self, indicators_df, holding):
        """
        Convert continuous indicator values to discrete states including holding position.
        Following Q-Trader hints: state includes indicators + current holding.
        """
        # Normalize indicators using percentile-based bounds for better distribution
        bbp_norm = np.clip(indicators_df['BBP'].iloc[0], 0, 1)
        
        sma_ratio = indicators_df['SMA_RATIO'].iloc[0]
        sma_norm = np.clip((sma_ratio - 0.9) / 0.2, 0, 1)  # 0.9 to 1.1 range
        
        rsi = indicators_df['RSI'].iloc[0]
        rsi_norm = np.clip(rsi / 100.0, 0, 1)

        # Discretize indicators into bins
        bbp_disc = min(int(bbp_norm * self.bins), self.bins - 1)
        sma_disc = min(int(sma_norm * self.bins), self.bins - 1)
        rsi_disc = min(int(rsi_norm * self.bins), self.bins - 1)

        # Add holding state: 0=SHORT, 1=CASH, 2=LONG
        if holding == -1000:
            holding_state = 0
        elif holding == 0:
            holding_state = 1
        else:  # holding == 1000
            holding_state = 2

        # Combine into single state: indicators + holding
        # State space: bins^3 * 3 holding states
        state = (bbp_disc * (self.bins ** 2) + sma_disc * self.bins + rsi_disc) * 3 + holding_state
        return state

    def add_evidence(self, symbol="IBM", sd=dt.datetime(2008, 1, 1), ed=dt.datetime(2009, 1, 1), sv=10000):
        """
        Trains your strategy learner over a given time frame following Q-Trader methodology.
        """
        # Get price data
        dates = pd.date_range(sd, ed)
        price_df = ut.get_data([symbol], dates, addSPY=True, colname='Adj Close')
        price_df = price_df.drop(['SPY'], axis=1, errors='ignore')

        # Get indicators
        indicators_df = get_all_indicators(price_df, symbol)
        prices = price_df[symbol].values

        # Training parameters following Q-Trader hints
        N = 5  # N-day future return for reward calculation
        epochs = 200  # Multiple training iterations for convergence
        
        if self.verbose:
            print(f"Training QLearner for {symbol} from {sd} to {ed}")
            print(f"Data shape: {price_df.shape}")

        # Training loop - multiple epochs for convergence
        for epoch in range(epochs):
            position = 0  # Start with no position: -1000=SHORT, 0=CASH, 1000=LONG
            
            # Initialize first state
            if len(indicators_df) > 0:
                current_state = self.discretize_indicators(indicators_df.iloc[[0]], position)
                action = self.learner.querysetstate(current_state)
            
            # Step through each day in training data
            for i in range(1, len(prices) - N):
                # Convert action to position: 0=SHORT(-1000), 1=CASH(0), 2=LONG(1000)
                if action == 0:
                    new_position = -1000
                elif action == 1:
                    new_position = 0
                else:  # action == 2
                    new_position = 1000
                
                # Calculate reward based on N-day future return (Q-Trader methodology)
                future_price = prices[i + N] if i + N < len(prices) else prices[-1]
                current_price = prices[i]
                future_return = (future_price / current_price) - 1.0
                
                # Reward based on position profitability
                if new_position > 0:  # Long position
                    reward = future_return * 1000  # Profit when price goes up
                elif new_position < 0:  # Short position
                    reward = -future_return * 1000  # Profit when price goes down
                else:  # Cash position
                    reward = 0  # No market exposure
                
                # Transaction cost penalty for position changes
                if new_position != position:
                    reward -= 10.0
                
                # Update position
                position = new_position
                
                # Calculate next state and query learner
                next_state = self.discretize_indicators(indicators_df.iloc[[i]], position)
                action = self.learner.query(next_state, reward)

        self.trained = True
        if self.verbose:
            print("Training completed!")

    def testPolicy(self, symbol="IBM", sd=dt.datetime(2009, 1, 1), ed=dt.datetime(2010, 1, 1), sv=10000):
        """
        Tests your learner using data outside of the training data
        """
        if not self.trained:
            raise ValueError("Strategy learner has not been trained yet!")

        # Get price data
        dates = pd.date_range(sd, ed)
        price_df = ut.get_data([symbol], dates, addSPY=True, colname='Adj Close')
        price_df = price_df.drop(['SPY'], axis=1, errors='ignore')

        # Get indicators
        indicators_df = get_all_indicators(price_df, symbol)

        # Initialize trades DataFrame
        trades = price_df.copy()
        trades.values[:, :] = 0  # Set all values to 0

        # Test policy (no learning, only exploitation)
        position = 0  # Start with no position
        
        for i in range(len(indicators_df)):
            # Calculate current state
            current_state = self.discretize_indicators(indicators_df.iloc[[i]], position)
            
            # Query learner for action (no learning)
            action = self.learner.querysetstate(current_state)
            
            # Convert action to position: 0=SHORT(-1000), 1=CASH(0), 2=LONG(1000)
            if action == 0:
                new_position = -1000
            elif action == 1:
                new_position = 0
            else:  # action == 2
                new_position = 1000
            
            # Record trade if position changes
            if new_position != position:
                trades.iloc[i, 0] = new_position - position
                position = new_position

        if self.verbose:
            print(f"Testing completed. Total trades: {(trades != 0).sum().iloc[0]}")

        return trades
