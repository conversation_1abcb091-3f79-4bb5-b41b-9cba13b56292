import pandas as pd
import numpy as np
from util import get_data

def author():
    return 'jjang333'

def BBP(prices, window=20):
    sma = prices.rolling(window).mean()
    std = prices.rolling(window).std()
    upper = sma + 2 * std
    lower = sma - 2 * std
    bbp = (prices - lower) / (upper - lower)
    return bbp

def SMA_ratio(prices, window=20):
    sma = prices.rolling(window).mean()
    return prices / sma

def RSI(prices, window=14):
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def momentum(prices, window=10):
    return (prices / prices.shift(window)) - 1

def MACD_hist(prices, short_win=12, long_win=26, signal_win=9):
    ema_short = prices.ewm(span=short_win, adjust=False).mean()
    ema_long = prices.ewm(span=long_win, adjust=False).mean()
    macd = ema_short - ema_long
    signal = macd.ewm(span=signal_win, adjust=False).mean()
    hist = macd - signal
    return hist

def Golden_Cross(prices, short_window=50, long_window=200):
    """
    Golden Cross indicator using SMA(50) and SMA(200).

    Returns a vector with:
        +1 when short SMA crosses above long SMA (Golden Cross - bullish),
        -1 when short SMA crosses below long SMA (Death Cross - bearish),
         0 otherwise.
    """

    short_sma = prices.rolling(window=short_window, min_periods=1).mean()
    long_sma = prices.rolling(window=long_window, min_periods=1).mean()

    # Crossover detection: compare today's and yesterday's SMA difference
    signal = pd.Series(0.0, index=prices.index)

    # Difference in SMAs
    diff = short_sma - long_sma
    prev_diff = diff.shift(1)

    # Golden Cross: was below, now above
    signal[(prev_diff < 0) & (diff > 0)] = 1.0

    # Death Cross: was above, now below
    signal[(prev_diff > 0) & (diff < 0)] = -1.0

    return signal

def get_all_indicators(price_df, symbol):
    """
    Calculate the 3 best indicators for the given price data and return as DataFrame.

    Args:
        price_df: DataFrame with price data (should have symbol as column)
        symbol: String symbol name to extract prices from price_df

    Returns:
        DataFrame with columns: BBP, SMA_RATIO, RSI (3 best performing indicators)
    """
    # Extract price series for the symbol
    prices = price_df[symbol]

    # Calculate the 3 best indicators
    bbp = BBP(prices)
    sma_ratio = SMA_ratio(prices)
    rsi = RSI(prices)

    # Create DataFrame with the 3 best indicators
    indicators_df = pd.DataFrame(index=price_df.index)
    indicators_df['BBP'] = bbp
    indicators_df['SMA_RATIO'] = sma_ratio
    indicators_df['RSI'] = rsi

    return indicators_df

def run():
    import matplotlib.pyplot as plt
    import datetime as dt

    start_date = dt.datetime(2008, 1, 1)
    end_date = dt.datetime(2009, 12, 31)
    symbol = 'JPM'
    dates = pd.date_range(start_date, end_date)
    prices = get_data([symbol], dates)[symbol].fillna(method="ffill").fillna(method="bfill")

    indicators = {
        "BBP": BBP(prices),
        "SMA Ratio": SMA_ratio(prices),
        "RSI": RSI(prices),
        "Momentum": momentum(prices),
        "MACD Histogram": MACD_hist(prices)
    }

    for name, data in indicators.items():
        plt.figure(figsize=(10, 5))
        plt.plot(prices / prices.iloc[0], label="Normalized Price")
        plt.plot(data, label=name, color="orange")
        plt.title(f"{name} vs Normalized Price")
        plt.xlabel("Date")
        plt.ylabel("Value")
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(f"{name}.png")
        plt.close()

if __name__ == "__main__":
    run()
