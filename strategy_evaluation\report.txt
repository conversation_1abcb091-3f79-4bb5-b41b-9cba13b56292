STRATEGY EVALUATION PROJECT REPORT
===================================

Author: <PERSON> (jjang333)
Date: July 2025
Symbol: JPM
In-sample period: January 1, 2008 to December 31, 2009
Out-of-sample period: January 1, 2010 to December 31, 2011
Starting portfolio value: $100,000

EXECUTIVE SUMMARY
=================

This report presents a comprehensive analysis comparing a rule-based Manual Strategy against a Q-Learning-based Strategy Learner for trading JPM stock. The Manual Strategy achieved superior performance with 70.91% cumulative return in-sample and 7.48% out-of-sample, significantly outperforming both the benchmark and Strategy Learner. The analysis includes detailed experiments on strategy comparison and market impact effects.

INDICATOR OVERVIEW
==================

Three technical indicators were utilized in both Manual Strategy and Strategy Learner:

1. Bollinger Band Position (BBP)
   - Measures price position relative to Bollinger Bands (20-day SMA ± 2 standard deviations)
   - Values: 0 (at lower band) to 1 (at upper band)
   - Purpose: Identifies overbought (BBP > 0.8) and oversold (BBP < 0.2) conditions
   - Signal: Mean reversion opportunities when price approaches band extremes

2. Simple Moving Average Ratio (SMA_RATIO)
   - Ratio of current price to 20-day Simple Moving Average
   - Values: <1 (price below SMA) to >1 (price above SMA)
   - Purpose: Trend identification and momentum confirmation
   - Signal: Buy when ratio < 0.95, sell when ratio > 1.05

3. Relative Strength Index (RSI)
   - Momentum oscillator measuring speed and magnitude of price changes (14-day period)
   - Values: 0 to 100
   - Purpose: Identifies overbought (RSI > 70) and oversold (RSI < 30) conditions
   - Signal: Contrarian signals at extreme levels

These three indicators provide complementary perspectives: BBP for mean reversion, SMA_RATIO for trend following, and RSI for momentum analysis, creating a robust foundation for trading decisions.

MANUAL STRATEGY
===============

Strategy Description:
The Manual Strategy employs a rule-based approach requiring consensus from at least 2 out of 3 indicators before executing trades. The strategy maintains position constraints of -1000, 0, or +1000 shares.

Trading Rules:
- BUY Signal (2+ conditions must be met):
  * BBP < 0.2 (price near lower Bollinger Band)
  * SMA_RATIO < 0.95 (price below moving average)
  * RSI < 30 (oversold momentum)

- SELL Signal (2+ conditions must be met):
  * BBP > 0.8 (price near upper Bollinger Band)
  * SMA_RATIO > 1.05 (price above moving average)
  * RSI > 70 (overbought momentum)

- HOLD: When fewer than 2 indicators agree or conflicting signals occur

Performance Results:

                        In-Sample (2008-2009)    Out-of-Sample (2010-2011)
                        Manual    Benchmark       Manual    Benchmark
Cumulative Return       70.91%    1.23%          7.48%     -8.36%
Mean Daily Return       0.1124%   0.0169%        0.0172%   -0.0137%
Std Daily Return        1.099%    1.704%         0.753%    0.850%
Sharpe Ratio            1.6236    0.1572         0.3620    -0.2567
Number of Trades        24        2              14        2

Analysis:
The Manual Strategy significantly outperformed the benchmark in both periods. The in-sample performance (70.91% vs 1.23%) demonstrates the strategy's effectiveness during the 2008-2009 financial crisis, where mean reversion and oversold conditions provided profitable opportunities. The out-of-sample performance (7.48% vs -8.36%) shows the strategy's robustness, maintaining positive returns while the benchmark declined.

The performance difference between in-sample and out-of-sample periods reflects different market conditions. The 2008-2009 period featured high volatility and clear mean reversion patterns ideal for the strategy's indicators. The 2010-2011 period showed more moderate volatility with fewer extreme indicator readings, resulting in fewer trading opportunities but still positive performance.

The strategy successfully obeys holding constraints, maintaining positions of -1000, 0, or +1000 shares throughout both periods.

STRATEGY LEARNER
================

Method Description:
The Strategy Learner utilizes Q-Learning, a reinforcement learning algorithm, to develop trading strategies. The implementation consists of:

1. State Space Discretization:
   - Three indicators (BBP, SMA_RATIO, RSI) are normalized and discretized into 10 bins each
   - Total state space: 10³ = 1000 possible states
   - State encoding: state = BBP_bin × 100 + SMA_bin × 10 + RSI_bin

2. Action Space:
   - Three actions: SELL (0), HOLD (1), BUY (2)
   - Actions translate to target positions: -1000, current position, +1000 shares

3. Reward Function:
   - Primary reward: Future 5-day return × 100 (scaled for learning)
   - Position-based reward: Positive for profitable position changes
   - Transaction cost penalty: -0.01 for each trade

4. Learning Parameters:
   - Learning rate (alpha): 0.2
   - Discount factor (gamma): 0.9
   - Initial exploration rate (rar): 0.5 with decay (radr): 0.99
   - Training epochs: 100

5. Training Process:
   - For each epoch, step through historical data
   - Use query() method to update Q-table based on rewards
   - Gradually reduce exploration as learning progresses

6. Testing Process:
   - Set exploration rate to 0 (no random actions)
   - Use querysetstate() method to select optimal actions
   - Convert actions to actual trades based on position changes

The Q-Learning approach learns optimal state-action mappings through trial and error, potentially discovering non-obvious patterns in the data that rule-based strategies might miss.

EXPERIMENT 1: MANUAL STRATEGY vs STRATEGY LEARNER
=================================================

Objective:
Compare the performance of Manual Strategy against Strategy Learner across both in-sample and out-of-sample periods to evaluate the effectiveness of rule-based versus machine learning approaches.

Methodology:
- Both strategies trained/optimized on in-sample data (2008-2009)
- Both strategies tested on out-of-sample data (2010-2011)
- Identical transaction costs: $9.95 commission, 0.5% market impact
- Performance metrics: cumulative return, daily return statistics, Sharpe ratio, trading activity

Results:

                        In-Sample (2008-2009)              Out-of-Sample (2010-2011)
Metric                  Manual    Learner   Benchmark     Manual    Learner   Benchmark
Cumulative Return       70.91%    11.52%    1.23%        7.48%     0.42%     -8.36%
Mean Daily Return       0.1124%   0.0311%   0.0169%      0.0172%   0.0042%   -0.0137%
Std Daily Return        1.099%    1.374%    1.704%       0.753%    0.819%    0.850%
Sharpe Ratio            1.6236    0.3589    0.1572       0.3620    0.0810    -0.2567
Number of Trades        24        7         2            14        19        2

Analysis:
The Manual Strategy significantly outperformed the Strategy Learner in both periods. Several factors explain this outcome:

1. Market Conditions: The 2008-2009 period featured clear mean reversion patterns that the rule-based approach effectively captured, while the Q-Learning algorithm may have struggled with the limited training data during extreme market conditions.

2. Overfitting: The Strategy Learner showed signs of overfitting, with performance degrading from 11.52% in-sample to 0.42% out-of-sample, while the Manual Strategy maintained more consistent performance.

3. Sample Size: Q-Learning typically requires extensive data for effective learning. The 2-year training period may be insufficient for the algorithm to learn robust patterns.

4. State Space Complexity: With 1000 possible states and limited training data, many state-action pairs may not have been adequately explored during training.

The experiment demonstrates that well-designed rule-based strategies can outperform machine learning approaches, particularly when domain expertise is effectively incorporated into the trading rules.

EXPERIMENT 2: IMPACT OF MARKET IMPACT ON STRATEGY PERFORMANCE
=============================================================

Objective:
Analyze how varying levels of market impact affect the Strategy Learner's performance and trading behavior to understand the strategy's sensitivity to transaction costs.

Methodology:
- Test Strategy Learner with market impact values: 0.000, 0.005, 0.010, 0.020, 0.050
- Fixed commission: $9.95 per trade
- Measure performance metrics and trading activity at each impact level
- Analyze correlations between impact and strategy behavior

Results:

Impact    In-Sample Return    Out-Sample Return    In-Sample Sharpe    Out-Sample Sharpe    In-Trades    Out-Trades
0.000     14.54%             4.37%                0.4280              0.2335               9            3
0.005     1.65%              4.62%                0.1520              0.2432               1            1
0.010     25.71%             -8.71%               0.6775              -0.2572              19           17
0.020     7.01%              4.06%                0.2628              0.2211               7            1
0.050     -33.32%            -120.61%             -0.5105             -0.8295              13           37

Analysis:

1. Performance Sensitivity:
   - Best in-sample performance at 0.010 impact (25.71% return)
   - Best out-of-sample performance at 0.005 impact (4.62% return)
   - Severe performance degradation at 0.050 impact (-33.32% in-sample, -120.61% out-of-sample)

2. Trading Activity Correlation:
   - In-sample: Moderate positive correlation (0.284) between impact and trading activity
   - Out-of-sample: Strong positive correlation (0.840) between impact and trading activity
   - Higher impact levels paradoxically led to more trading, suggesting suboptimal learning

3. Strategy Robustness:
   - Strategy shows high sensitivity to market impact
   - Performance becomes highly volatile at impact levels above 0.010
   - Significant overfitting observed (845.5% average performance degradation from in-sample to out-of-sample)

4. Optimal Impact Level:
   - For practical implementation, 0.005 impact provides the best balance of in-sample and out-of-sample performance
   - Impact levels above 0.020 result in consistently poor performance

The experiment reveals that the Strategy Learner is highly sensitive to transaction costs, with performance deteriorating rapidly as market impact increases. This sensitivity highlights the importance of considering realistic transaction costs in strategy development and the need for more robust learning algorithms in high-cost trading environments.

CONCLUSIONS
===========

1. Manual Strategy Superiority: The rule-based Manual Strategy significantly outperformed both the benchmark and Q-Learning Strategy Learner, demonstrating the value of domain expertise in strategy design.

2. Indicator Effectiveness: The combination of BBP, SMA_RATIO, and RSI provided robust signals across different market conditions, with the consensus-based approach reducing false signals.

3. Q-Learning Limitations: The Strategy Learner showed promise but suffered from overfitting and sensitivity to transaction costs, suggesting the need for more sophisticated learning approaches or longer training periods.

4. Transaction Cost Impact: Market impact significantly affects strategy performance, with the Strategy Learner showing particular sensitivity to higher transaction costs.

5. Market Condition Dependency: Both strategies performed better during the volatile 2008-2009 period compared to the more stable 2010-2011 period, highlighting the importance of market regime considerations.

This analysis demonstrates that while machine learning approaches offer potential for strategy development, carefully designed rule-based strategies incorporating domain expertise can provide superior and more consistent performance, particularly in environments with significant transaction costs.

CHART DESCRIPTIONS
==================

The following charts were generated to support the analysis:

1. Manual Strategy Charts (manual_strategy_in_sample.png, manual_strategy_out_of_sample.png):
   - Purple line: Benchmark portfolio value normalized to 1.0
   - Red line: Manual Strategy portfolio value normalized to 1.0
   - Blue vertical lines: Long entry points (BUY signals)
   - Black vertical lines: Short entry points (SELL signals)
   - Charts clearly show the Manual Strategy's superior performance and trading activity

2. Experiment 1 Comparison Chart (experiment1_comparison.png):
   - Compares normalized portfolio values for Benchmark, Manual Strategy, and Strategy Learner
   - Separate subplots for in-sample and out-of-sample periods
   - Demonstrates Manual Strategy's consistent outperformance

3. Experiment 2 Impact Analysis Chart (experiment2_impact_analysis.png):
   - Four-panel chart showing impact of market impact on various metrics
   - Panel 1: Cumulative returns vs market impact
   - Panel 2: Sharpe ratios vs market impact
   - Panel 3: Trading activity vs market impact
   - Panel 4: Return per trade vs market impact
   - Illustrates the Strategy Learner's sensitivity to transaction costs

TECHNICAL IMPLEMENTATION NOTES
==============================

1. Holding Constraints: Both strategies strictly maintain positions of -1000, 0, or +1000 shares, never exceeding these limits.

2. Symbol and Date Compliance: Analysis uses JPM symbol with exact date ranges specified (2008-2009 in-sample, 2010-2011 out-of-sample).

3. Benchmark Definition: Benchmark strategy consists of buying 1000 shares on the first day and holding until the last day.

4. Performance Calculation: Cumulative return calculated as (final_value / initial_value) - 1.0, consistent with project requirements.

5. Transaction Costs: All strategies incorporate realistic transaction costs (commission and market impact) for accurate performance assessment.

REPRODUCIBILITY
===============

The strategies and experiments described in this report can be reproduced using the provided code with the following key parameters:

Manual Strategy:
- Indicators: BBP (20-day), SMA_RATIO (20-day), RSI (14-day)
- Decision rule: Require 2+ indicator consensus
- Thresholds: BBP (0.2, 0.8), SMA_RATIO (0.95, 1.05), RSI (30, 70)

Strategy Learner:
- State space: 1000 states (10×10×10 discretization)
- Action space: 3 actions (SELL, HOLD, BUY)
- Q-Learning parameters: α=0.2, γ=0.9, initial ε=0.5, decay=0.99
- Training: 100 epochs on in-sample data
- Testing: Zero exploration rate

The detailed implementation ensures that results can be replicated by following the described methodology and using the specified parameters.
