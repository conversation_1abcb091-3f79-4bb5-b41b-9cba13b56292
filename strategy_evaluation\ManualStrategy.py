import pandas as pd
import numpy as np
import datetime as dt
import matplotlib.pyplot as plt
from util import get_data
from indicators import get_all_indicators
import marketsimcode as ms

class ManualStrategy:
    def __init__(self, verbose=False, impact=0.005, commission=9.95):
        self.verbose = verbose
        self.impact = impact
        self.commission = commission
    
    def testPolicy(self, symbol="JPM", sd=dt.datetime(2008,1,1), ed=dt.datetime(2009,12,31), sv=100000):
        """
        Test the manual strategy and return trades DataFrame
        """
        # Get price data
        dates = pd.date_range(sd, ed)
        price_df = get_data([symbol], dates, addSPY=True, colname='Adj Close')
        price_df = price_df.drop(['SPY'], axis=1)
        
        # Get indicators
        indicators_df = get_all_indicators(price_df, symbol)
        
        # Initialize trades DataFrame
        trades = pd.DataFrame(index=price_df.index, columns=[symbol])
        trades = trades.fillna(0.0)
        
        # Current position: -1000 (short), 0 (neutral), 1000 (long)
        position = 0
        
        # Manual trading rules based on indicators
        for i in range(len(indicators_df)):
            if i == 0:  # Skip first row due to potential NaN values
                continue
                
            # Get current indicator values (using only 3 best indicators)
            bbp = indicators_df.iloc[i]['BBP']
            sma_ratio = indicators_df.iloc[i]['SMA_RATIO']
            rsi = indicators_df.iloc[i]['RSI']

            # Trading signals
            signal = 0

            # Buy signals (multiple conditions must be met)
            buy_signals = 0
            if bbp < 0.2:  # Price near lower Bollinger Band (mean reversion)
                buy_signals += 1
            if sma_ratio < 0.95:  # Price below SMA (trend following)
                buy_signals += 1
            if rsi < 30:  # RSI oversold (momentum)
                buy_signals += 1

            # Sell signals (multiple conditions must be met)
            sell_signals = 0
            if bbp > 0.8:  # Price near upper Bollinger Band (mean reversion)
                sell_signals += 1
            if sma_ratio > 1.05:  # Price above SMA (trend following)
                sell_signals += 1
            if rsi > 70:  # RSI overbought (momentum)
                sell_signals += 1

            # Decision logic: need at least 2 out of 3 indicators to agree
            if buy_signals >= 2 and sell_signals < 2:
                signal = 1  # Buy signal
            elif sell_signals >= 2 and buy_signals < 2:
                signal = -1  # Sell signal
            else:
                signal = 0  # Hold
            
            # Execute trades based on signal and current position
            if signal == 1 and position <= 0:  # Buy signal
                if position == 0:
                    trades.iloc[i][symbol] = 1000  # Buy 1000 shares
                    position = 1000
                elif position == -1000:
                    trades.iloc[i][symbol] = 2000  # Cover short and go long
                    position = 1000
                    
            elif signal == -1 and position >= 0:  # Sell signal
                if position == 0:
                    trades.iloc[i][symbol] = -1000  # Short 1000 shares
                    position = -1000
                elif position == 1000:
                    trades.iloc[i][symbol] = -2000  # Sell long and go short
                    position = -1000
        
        if self.verbose:
            print(f"Manual Strategy trades for {symbol}:")
            print(f"Total trades: {(trades != 0).sum().sum()}")
            print(f"Long entries: {(trades > 0).sum().sum()}")
            print(f"Short entries: {(trades < 0).sum().sum()}")
        
        return trades
    
    def benchmark_strategy(self, symbol="JPM", sd=dt.datetime(2008,1,1), ed=dt.datetime(2009,12,31), sv=100000):
        """
        Create benchmark strategy (buy and hold)
        """
        dates = pd.date_range(sd, ed)
        price_df = get_data([symbol], dates, addSPY=True, colname='Adj Close')
        price_df = price_df.drop(['SPY'], axis=1)
        
        trades = pd.DataFrame(index=price_df.index, columns=[symbol])
        trades = trades.fillna(0.0)
        trades.iloc[0][symbol] = 1000  # Buy 1000 shares on first day
        
        return trades
    
    def evaluate_strategy(self, symbol="JPM", sd=dt.datetime(2008,1,1), ed=dt.datetime(2009,12,31), sv=100000):
        """
        Evaluate and plot the manual strategy vs benchmark
        """
        # Get trades
        manual_trades = self.testPolicy(symbol, sd, ed, sv)
        benchmark_trades = self.benchmark_strategy(symbol, sd, ed, sv)
        
        # Calculate portfolio values
        manual_portvals = ms.compute_portvals(manual_trades, start_val=sv, commission=self.commission, impact=self.impact)
        benchmark_portvals = ms.compute_portvals(benchmark_trades, start_val=sv, commission=self.commission, impact=self.impact)
        
        # Normalize to start at 1.0
        manual_portvals = manual_portvals / manual_portvals.iloc[0]
        benchmark_portvals = benchmark_portvals / benchmark_portvals.iloc[0]

        # Create plot with trade markers for saving (NO DISPLAY, ONLY SAVE)
        plt.figure(figsize=(12, 8))
        plt.plot(benchmark_portvals.index, benchmark_portvals.values, 'purple', label='Benchmark', linewidth=2)
        plt.plot(manual_portvals.index, manual_portvals.values, 'red', label='Manual Strategy', linewidth=2)

        # Add entry points
        for date in manual_trades.index:
            if manual_trades.loc[date][symbol] > 0:
                plt.axvline(x=date, color='blue', linestyle='-', alpha=0.7, linewidth=1)
            elif manual_trades.loc[date][symbol] < 0:
                plt.axvline(x=date, color='black', linestyle='-', alpha=0.7, linewidth=1)

        plt.xlabel('Date')
        plt.ylabel('Normalized Portfolio Value')
        plt.title(f'Manual Strategy vs Benchmark - {symbol}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        # Save plot
        period_str_file = "in_sample" if sd.year == 2008 else "out_of_sample"
        plt.savefig(f'manual_strategy_{period_str_file}.png', dpi=300, bbox_inches='tight')
        plt.close()  # Close the figure to avoid showing it twice
        
        return manual_portvals, benchmark_portvals
    
    def get_performance_stats(self, portvals):
        """
        Calculate performance statistics
        """
        daily_returns = portvals.pct_change().dropna()
        
        cumulative_return = (portvals.iloc[-1] / portvals.iloc[0]) - 1
        mean_daily_return = daily_returns.mean()
        std_daily_return = daily_returns.std()
        
        return {
            'Cumulative Return': cumulative_return,
            'Mean Daily Return': mean_daily_return,
            'Std Daily Return': std_daily_return
        }

    def generate_charts(self):
        """
        Generate charts for manual strategy analysis.
        This method creates comparison charts for both in-sample and out-of-sample periods.
        """
        print("Generating Manual Strategy charts...")

        # In-sample analysis
        print("Analyzing in-sample period (2008-2009)...")
        manual_portvals_in, benchmark_portvals_in = self.evaluate_strategy(
            symbol="JPM",
            sd=dt.datetime(2008,1,1),
            ed=dt.datetime(2009,12,31),
            sv=100000
        )

        # Out-of-sample analysis
        print("Analyzing out-of-sample period (2010-2011)...")
        manual_portvals_out, benchmark_portvals_out = self.evaluate_strategy(
            symbol="JPM",
            sd=dt.datetime(2010,1,1),
            ed=dt.datetime(2011,12,31),
            sv=100000
        )

        # Print performance statistics
        print("\n=== PERFORMANCE STATISTICS ===")

        # In-sample stats
        manual_stats_in = self.get_performance_stats(manual_portvals_in)
        benchmark_stats_in = self.get_performance_stats(benchmark_portvals_in)

        print("In-Sample Performance:")
        print(f"Manual Strategy - Cumulative Return: {manual_stats_in['Cumulative Return']:.6f}")
        print(f"Benchmark - Cumulative Return: {benchmark_stats_in['Cumulative Return']:.6f}")
        print(f"Manual Strategy - Mean Daily Return: {manual_stats_in['Mean Daily Return']:.6f}")
        print(f"Benchmark - Mean Daily Return: {benchmark_stats_in['Mean Daily Return']:.6f}")
        print(f"Manual Strategy - Std Daily Return: {manual_stats_in['Std Daily Return']:.6f}")
        print(f"Benchmark - Std Daily Return: {benchmark_stats_in['Std Daily Return']:.6f}")

        # Out-of-sample stats
        manual_stats_out = self.get_performance_stats(manual_portvals_out)
        benchmark_stats_out = self.get_performance_stats(benchmark_portvals_out)

        print("\nOut-of-Sample Performance:")
        print(f"Manual Strategy - Cumulative Return: {manual_stats_out['Cumulative Return']:.6f}")
        print(f"Benchmark - Cumulative Return: {benchmark_stats_out['Cumulative Return']:.6f}")
        print(f"Manual Strategy - Mean Daily Return: {manual_stats_out['Mean Daily Return']:.6f}")
        print(f"Benchmark - Mean Daily Return: {benchmark_stats_out['Mean Daily Return']:.6f}")
        print(f"Manual Strategy - Std Daily Return: {manual_stats_out['Std Daily Return']:.6f}")
        print(f"Benchmark - Std Daily Return: {benchmark_stats_out['Std Daily Return']:.6f}")

        print("Manual Strategy charts generated successfully!")

    def author(self):
        return 'jjang333'

# Example usage and testing
if __name__ == "__main__":
    # Test the manual strategy
    ms_trader = ManualStrategy(verbose=True)
    
    # In-sample period
    print("=== IN-SAMPLE PERIOD ===")
    manual_portvals_in, benchmark_portvals_in = ms_trader.evaluate_strategy(
        symbol="JPM", 
        sd=dt.datetime(2008,1,1), 
        ed=dt.datetime(2009,12,31), 
        sv=100000
    )
    
    # Out-of-sample period
    print("\n=== OUT-OF-SAMPLE PERIOD ===")
    manual_portvals_out, benchmark_portvals_out = ms_trader.evaluate_strategy(
        symbol="JPM", 
        sd=dt.datetime(2010,1,1), 
        ed=dt.datetime(2011,12,31), 
        sv=100000
    )
    
    # Performance statistics
    print("\n=== PERFORMANCE STATISTICS ===")
    
    # In-sample stats
    manual_stats_in = ms_trader.get_performance_stats(manual_portvals_in)
    benchmark_stats_in = ms_trader.get_performance_stats(benchmark_portvals_in)
    
    print("In-Sample Performance:")
    print(f"Manual Strategy - Cumulative Return: {manual_stats_in['Cumulative Return']:.6f}")
    print(f"Benchmark - Cumulative Return: {benchmark_stats_in['Cumulative Return']:.6f}")
    print(f"Manual Strategy - Mean Daily Return: {manual_stats_in['Mean Daily Return']:.6f}")
    print(f"Benchmark - Mean Daily Return: {benchmark_stats_in['Mean Daily Return']:.6f}")
    print(f"Manual Strategy - Std Daily Return: {manual_stats_in['Std Daily Return']:.6f}")
    print(f"Benchmark - Std Daily Return: {benchmark_stats_in['Std Daily Return']:.6f}")
    
    # Out-of-sample stats
    manual_stats_out = ms_trader.get_performance_stats(manual_portvals_out)
    benchmark_stats_out = ms_trader.get_performance_stats(benchmark_portvals_out)
    
    print("\nOut-of-Sample Performance:")
    print(f"Manual Strategy - Cumulative Return: {manual_stats_out['Cumulative Return']:.6f}")
    print(f"Benchmark - Cumulative Return: {benchmark_stats_out['Cumulative Return']:.6f}")
    print(f"Manual Strategy - Mean Daily Return: {manual_stats_out['Mean Daily Return']:.6f}")
    print(f"Benchmark - Mean Daily Return: {benchmark_stats_out['Mean Daily Return']:.6f}")
    print(f"Manual Strategy - Std Daily Return: {manual_stats_out['Std Daily Return']:.6f}")
    print(f"Benchmark - Std Daily Return: {benchmark_stats_out['Std Daily Return']:.6f}")