import numpy as np
import <PERSON>g<PERSON>earner as bl
import LinRegLearner as lrl
class InsaneLearner(object):
    def __init__(self, verbose=False):
        self.learners = [bl.BagLearner(learner=lrl.LinRegLearner, kwargs={"verbose": False}, bags=20, boost=False, verbose=verbose) for _ in range(20)]
        self.verbose = verbose
    def add_evidence(self, Xtrain, Ytrain):
        for learner in self.learners:
            learner.add_evidence(Xtrain, Ytrain)
    def query(self, Xtest):
        preds = np.array([learner.query(Xtest) for learner in self.learners])
        return np.mean(preds, axis=0)
    def author(self):  
        return 'jjang333'